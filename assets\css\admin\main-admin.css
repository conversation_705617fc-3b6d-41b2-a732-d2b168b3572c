/* ==========================================================================
   Include global partials - begin
   ========================================================================== */

/* ==========================================================================
   Common variables - begin
   ========================================================================== */

/* ==========================================================================
   Common variables - end
   ========================================================================== */

/* ==========================================================================
   Responsive variables - begin
   ========================================================================== */

/* ==========================================================================
   Responsive variables - end
   ========================================================================== */

/* ==========================================================================
   Layout mixin style - begin
   ========================================================================== */

/* ==========================================================================
   Layout mixin style - end
   ========================================================================== */

/* ==========================================================================
   Animation mixin style - begin
   ========================================================================== */

/* ==========================================================================
   Animation mixin style - end
   ========================================================================== */

/* ==========================================================================
   Specific mixin style - end
   ========================================================================== */

/* ==========================================================================
   Specific mixin style - end
   ========================================================================== */

/* ==========================================================================
   Responsive mixin style - begin
   ========================================================================== */

/* ==========================================================================
   Responsive mixin style - end
   ========================================================================== */

/* ==========================================================================
   Include global partials - end
   ========================================================================== */

/* ==========================================================================
   Meta Boxes styles - begin
   ========================================================================== */

.evc-meta-box-page {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;
}

.evc-meta-box-page .evc-meta-box-field-holder {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;
  margin: 1em 0;
}

.evc-meta-box-page .evc-meta-box-label {
  display: block;
  margin: 0 0 5px;
}

.evc-meta-box-page .evc-meta-box-field {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;
}

.evc-meta-box-page .evc-meta-box-description {
  margin: 5px 0 0;
}

.evc-meta-box-page .evc-meta-box-image {
  position: relative;
  display: block;
  max-width: 120px;
  height: auto;
  margin: 0 0 1em;
}

/* ==========================================================================
   Meta Boxes styles - end
   ========================================================================== */

/*# sourceMappingURL=meta-boxes-map.css.map */
/* ==========================================================================
   Include global partials - begin
   ========================================================================== */

/* ==========================================================================
   Common variables - begin
   ========================================================================== */

/* ==========================================================================
   Common variables - end
   ========================================================================== */

/* ==========================================================================
   Responsive variables - begin
   ========================================================================== */

/* ==========================================================================
   Responsive variables - end
   ========================================================================== */

/* ==========================================================================
   Layout mixin style - begin
   ========================================================================== */

/* ==========================================================================
   Layout mixin style - end
   ========================================================================== */

/* ==========================================================================
   Animation mixin style - begin
   ========================================================================== */

/* ==========================================================================
   Animation mixin style - end
   ========================================================================== */

/* ==========================================================================
   Specific mixin style - end
   ========================================================================== */

/* ==========================================================================
   Specific mixin style - end
   ========================================================================== */

/* ==========================================================================
   Responsive mixin style - begin
   ========================================================================== */

/* ==========================================================================
   Responsive mixin style - end
   ========================================================================== */

/* ==========================================================================
   Include global partials - end
   ========================================================================== */

/* ==========================================================================
   Options styles - begin
   ========================================================================== */

.evc-admin-about .evc-admin-about-content {
  position: relative;
  display: inline-block;
  width: 100%;
  vertical-align: top;
  padding-right: 160px;
  box-sizing: border-box;
}

.evc-admin-about .evc-admin-logo {
  position: absolute;
  top: 0;
  right: 0;
  display: block;
  width: auto;
}

.evc-admin-about .evc-admin-image {
  position: relative;
  display: block;
  margin: 50px 0 0;
}

.evc-admin-settings .evc-admin-checkboxes {
  margin-bottom: 10px;
}

.evc-admin-settings .evc-admin-checkboxes:after {
  content: ' ';
  display: table;
  clear: both;
}

.evc-admin-settings .evc-admin-checkboxes .evc-admin-checkbox {
  float: left;
  width: 25%;
}

@media screen and (min-width: 783px) {
  .evc-admin-settings .evc-admin-checkboxes .evc-admin-checkbox:nth-child(4n+1) {
    clear: both;
  }
}

@media screen and (max-width: 782px) {
  .evc-admin-settings .evc-admin-checkboxes .evc-admin-checkbox {
    width: 50%;
  }

  .evc-admin-settings .evc-admin-checkboxes .evc-admin-checkbox:nth-child(2n+1) {
    clear: both;
  }
}

/* ==========================================================================
   Options styles - end
   ========================================================================== */

/*# sourceMappingURL=options-map.css.map */
/* ==========================================================================
   Shortcodes styles - begin
   ========================================================================== */

.evc-vc-custom-icon {
  background-position: 0 0 !important;
}

/* ==========================================================================
   Shortcodes styles - end
   ========================================================================== */

/*# sourceMappingURL=shortcodes-map.css.map */