=== Extensive VC Addons for WPBakery page builder ===
Contributors: nenad-obradovic
Tags: wpbakery, wpbakery addons, wpbakery page builder, shortcodes
Donate link: http://wprealize.com/donate/
Requires at least: 4.6
Tested up to: 5.8
Stable tag: 1.9.1
Requires PHP: 5.3
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Extensive addons or extensions for WPBakery page builder, which allows you to add unique, flexible and fully responsive shortcodes

== Description ==

<blockquote>Extensive WordPress Addons Plugin For WPBakery Page Builder</blockquote>

Extensive VC is a powerful WordPress tool which allows you to add unique, flexible and fully responsive shortcode elements on your site. It is an addons for premium plugin <a href="https://codecanyon.net/item/visual-composer-page-builder-for-wordpress/242431?ref=extensive-vc-addon" target="_blank"><strong>WPBakery Page Builder</strong></a>, so first you need to install/activate this required plugin to be able to use Extensive VC plugin. Plugin contains four packs of shortcode elements (classic, interactive, infographic and typography). All of these shortcode packs you can find in our website in Elements section inside header menu.

See the all of addons elements in the action here - <a href="http://wprealize.com/" target="_blank" title="Extensive VC Addons for WPBakery Page Builder"><strong>LIVE DEMO</strong></a>

<a href="https://github.com/NenadObradovic/extensive-vc" target="_blank" title="Extensive VC Addons - GitHub"><strong>GitHub Version</strong></a>

<h3>Features</h3>
<h4 style="margin-top: 1rem;">1. Shortcodes</h4>
<ul>
	<li><a href="http://wprealize.com/classic/blog-list/">Classic - Blog List (New)</a></li>
	<li><a href="http://wprealize.com/classic/button/">Classic - Button</a></li>
	<li><a href="http://wprealize.com/classic/clients/">Classic - Clients</a></li>
	<li><a href="http://wprealize.com/classic/icon-with-text/">Classic - Icon With Text</a></li>
	<li><a href="http://wprealize.com/classic/image-with-text/">Classic - Image With Text</a></li>
	<li><a href="http://wprealize.com/classic/pricing-table/">Classic - Pricing Table</a></li>
	<li><a href="http://wprealize.com/classic/separator/">Classic - Separator</a></li>
	<li><a href="http://wprealize.com/classic/single-image/">Classic - Single Image</a></li>
	<li><a href="http://wprealize.com/classic/tabs/">Classic - Tabs</a></li>
	<li><a href="http://wprealize.com/interactive/flip-image/">Interactive - Flip Image</a></li>
	<li><a href="http://wprealize.com/interactive/full-screen-sections/">Interactive - Full Screen Sections</a></li>
	<li><a href="http://wprealize.com/interactive/gallery-block/">Interactive - Gallery Block</a></li>
	<li><a href="http://wprealize.com/interactive/image-gallery/">Interactive - Image Gallery</a></li>
	<li><a href="http://wprealize.com/interactive/interactive-banner/">Interactive - Interactive Banner</a></li>
	<li><a href="http://wprealize.com/interactive/process/">Interactive - Process</a></li>
	<li><a href="http://wprealize.com/interactive/process-2/">Interactive - Process 2</a></li>
	<li><a href="http://wprealize.com/interactive/product-list/">Interactive - Product List (New)</a></li>
	<li><a href="http://wprealize.com/interactive/testimonials/">Interactive - Testimonials</a></li>
	<li><a href="http://wprealize.com/infographic/counter/">Infographic - Counter</a></li>
	<li><a href="http://wprealize.com/infographic/doughnut-chart/">Infographic - Doughnut Chart</a></li>
	<li><a href="http://wprealize.com/infographic/icon-progress-bar/">Infographic - Icon Progress Bar</a></li>
	<li><a href="http://wprealize.com/infographic/line-graph/">Infographic - Line Graph</a></li>
	<li><a href="http://wprealize.com/infographic/pie-chart/">Infographic - Pie Chart</a></li>
	<li><a href="http://wprealize.com/infographic/post-carousel/">Infographic - Post Carousel (New)</a></li>
	<li><a href="http://wprealize.com/infographic/progress-bar/">Infographic - Progress Bar</a></li>
	<li><a href="http://wprealize.com/infographic/text-marquee/">Infographic - Text Marquee</a></li>
	<li><a href="http://wprealize.com/typography/blockquote/">Typography - Blockquote</a></li>
	<li><a href="http://wprealize.com/typography/custom-font/">Typography - Custom Font</a></li>
	<li><a href="http://wprealize.com/typography/dropcaps/">Typography - Dropcaps</a></li>
	<li><a href="http://wprealize.com/typography/icon-list/">Typography - Icon List</a></li>
	<li><a href="http://wprealize.com/typography/section-title/">Typography - Section Title</a></li>
	<li><a href="http://wprealize.com/typography/svg-text/">Typography - SVG Text</a></li>
</ul>
<h4 style="margin-top: 1rem;">2. Custom Post Types</h4>
<ul>
	<li><a href="http://wprealize.com/classic/clients/">Clients</a></li>
	<li><a href="http://wprealize.com/interactive/testimonials/">Testimonials</a></li>
</ul>
<h4 style="margin-top: 1rem;">3. Widgets</h4>
<ul>
    <li>Blog List Widget</li>
    <li>Button Widget</li>
    <li>Contact Form 7 Widget</li>
    <li>Custom Font Widget</li>
    <li>Separator Widget</li>
    <li>Text Marquee Widget</li>
</ul>
<h4 style="margin-top: 1rem;">4. Plugins Compatibility</h4>
<ul>
    <li><a href="https://codecanyon.net/item/visual-composer-page-builder-for-wordpress/242431?ref=extensive-vc-addon" target="_blank">WPBakery Page Builder</a></li>
    <li><a href="https://wordpress.org/plugins/contact-form-7" target="_blank">Contact Form 7</a></li>
    <li><a href="https://wordpress.org/plugins/woocommerce/" target="_blank">WooCommerce</a></li>
</ul>
<p><strong>This is just the beginning...</strong></p>

Thank you for using Extensive VC plugin and for supporting us to continue improving the plugin with new features, shortcodes and options. A huge thanks in advance, WP Realize team!

== Installation ==

From your WordPress dashboard

1. Go to your WordPress Dashboard -> Plugins -> Add New
2. Search for Extensive VC
3. Activate Extensive VC from your Plugins page. (You'll be greeted with a Welcome page.)
4. Go to your pages and create some elements with WPBakery page builder and inside page builder add element you will see Extensive VC tab with our shortcodes
5. Visit your site and enjoy :)

From wordpress.org

1. Download Extensive VC.
2. Upload the extensive-vc directory to your ‘/wp-content/plugins/’ directory, using your favorite method (ftp, sftp, scp, etc…)
3. Activate Extensive VC from your Plugins page. (You'll be greeted with a Welcome page.)
4. Go to your pages and create some elements with WPBakery page builder and inside page builder add element you will see Extensive VC tab with our shortcodes
5. Visit your site and enjoy :)

== Frequently Asked Questions ==

= I activated the Extensive VC Plugin but I cannot see It In the dashboard panel? =

My plugin it is extension for WPBakery page builder so you must first install/activate WPBakery page builder plugin. After you activate the required plugin, the elements should be available for use in WPBakery page builder shortcode section.

== Screenshots ==

1. List of shortcodes in WPBakery page builder Add Element window.
2. Extensive VC plugin Settings page.
3. Editing a plugin shortcode element in WPBakery page builder.
4. Progress Bar shortcode example.
5. Blockquote shortcode example.

== Changelog ==

1.9.1
- Improved plugins security (WPScan)
- Updated language pot file

1.9
- Added WordPress 5.6 compatibility
- Improved button size option with Full Width value
- Updated Lightbox script to v2.11.2
- Updated Chart script to v2.8.0
- Updated language pot file

1.8.1
- Improved singleton getInstance method
- Updated Lightbox script to v2.11.1
- Fixed meta box save function to check only plugin meta box fields

1.8
- Renamed extensive_vc_is_visual_composer_installed function to extensive_vc_is_wpbakery_page_builder_installed
- Renamed Visual Composer labels with WPBakery Page Builder
- Fixed Custom CSS Class option for Text Marquee shortcode
- Fixed Custom Link rendering for Interactive Banner shortcode
- Fixed Extensive VC Settings -> Disable Shortcodes global options
- Updated language pot file

1.7.7
- Added Choose Slide Animation option for Full Screen Sections shortcode
- Fixed Custom CSS Class option for Tabs Item shortcode
- Fixed lightbox functionality for Single Image, Image With Text and Gallery Block shortcodes
- Updated language pot file

1.7.6
- Added Title options for Icon Progress Bar shortcode
- Improved plugin admin styles for Multisite
- Improved framework functions
- Removed unnecessary files
- Updated language pot file

*******
- Updated WordPress compatibility to 5.x

1.7.5
- Added Simple Strike Line On Hover 2 type value for Button shortcode and widget
- Added Image Size option for Testimonials shortcode
- Added Image Size option for Interactive Banner shortcode
- Added Content Background Color for Pricing Table Item shortcode
- Added Content Border Color for Pricing Table Item shortcode
- Updated language pot file

1.7.4
- Extended global window js object with evc object
- Improved Doughnut Chart shortcode js
- Improved Pie Chart shortcode js
- Improved plugin optimization and performance
- Improved framework functions

1.7.3
- Fixed category query for Blog List shortcode
- Fixed category query for Post Carousel shortcode
- Fixed button link for Pricing Table shortcode

1.7.2
- Added Post Carousel shortcode
- Added shortcode pagination functionality
- Added Pagination Type option for Blog List shortcode
- Added Button Options Group for Blog List shortcode to style pagination button
- Added Button Margin option for Pricing Table shortcode
- Updated language pot file

1.7.1
- Added Legend Text Size option for Doughnut Chart shortcode
- Added Legend Color option for Doughnut Chart shortcode
- Added Legend Text Size option for Pie Chart shortcode
- Added Legend Color option for Pie Chart shortcode
- Added Disable Ionicons Font option in general plugin settings panel
- Added Gallery Type option for Blog List shortcode
- Added Gallery Type option for Blog List widget
- Changed all core plugin scripts/styles id to be with dash instead of underscore
- Extend main plugin script with wp_localize_script and added possibilities to change slider navigation arrows with filter hook
- Renamed options description in general plugin settings panel
- Updated fullPage 3rd party library to 2.9.7
- Updated language pot file

1.7
- Added Blog List shortcode (5 layouts)
- Added Blog List widget
- Improved framework functions
- Updated language pot file

1.6.6
- Added extensive_vc_filter_shortcode_params hook
- Added hooks to change default value for shortcode parameters
- Added VC admin digit label for Counter shortcode
- Added VC admin text label for Custom Font shortcode
- Added VC admin letter label for Dropcaps shortcode
- Added VC admin text label for Icon List shortcode
- Added VC admin title label for Icon With Text shortcode
- Added VC admin title label for Image With Text shortcode
- Added VC admin title label for Interactive Banner shortcode
- Added VC admin title label for Section Title shortcode
- Fixed template overriding inside themes folder

= 1.6.5 =
- Added Order By option for Clients shortcode
- Added Order option for Clients shortcode
- Added Enable Slider Autoplay Hover Pause option for Clients shortcode
- Added Slide Margin (px) option for Clients shortcode
- Added Order By option for Testimonials shortcode
- Added Order option for Testimonials shortcode
- Added Enable Slider Autoplay Hover Pause option for Testimonials shortcode
- Added Slide Margin (px) option for Testimonials shortcode
- Added Enable Slider Autoplay Hover Pause option for Image Gallery shortcode
- Added Slide Margin (px) option for Image Gallery shortcode
- Updated language pot file

= 1.6.4 =
- Added Button Alignment option for Button shortcode
- Added Button Alignment option for Button widget
- Added Price Size option for Pricing Table shortcode
- Added Currency Size option for Pricing Table shortcode
- Added Price Period Size option for Pricing Table shortcode
- Added Line Color option for Line Graph shortcode
- Added Line Thickness option for Line Graph shortcode
- Added Disable Line option for Line Graph shortcode
- Added Fill Background Color option for Line Graph shortcode
- Updated language pot file
- Fixed button link for Pricing Table shortcode
- Fixed duplicated ids for pot file

= 1.6.3 =
- Added Line Graph shortcode
- Added SVG Text shortcode
- Added Custom Link option for Icon List shortcode
- Added Custom Link option for Icon With Text shortcode
- Updated Lightbox 3rd party library to 2.10.0
- Updated Owl Carousel 3rd party library to 2.3.4
- Updated language pot file

= 1.6.2 =
- Added WooCommerce compatibility
- Added Product List shortcode
- Added Type option for Clients shortcode
- Added Number Of Columns option for Clients shortcode
- Added Space Between Items option for Clients shortcode
- Added Enable Title option for Clients shortcode
- Added Title Tag option for Clients shortcode
- Added Title Color option for Clients shortcode
- Added Title Top Margin option for Clients shortcode
- Added Title Bottom Margin option for Clients shortcode
- Improved framework functions
- Updated language pot file

= 1.6.1 =
- Added Pricing Tables shortcode
- Added Separator widget
- Added Text Marquee widget
- Added Disable Widgets option in general plugin settings panel
- Improved framework functions
- Updated Chart.js 3rd party library to 2.7.2
- Updated language pot file

= 1.6 =
- Added Widgets functionality
- Added Button widget
- Added Contact Form 7 widget
- Added Custom Font widget
- Added Bordered Animation value for Image Behavior Type
- Improved framework functions
- Updated language pot file
- Changed button shortcode html tag from button to a

= 1.5.3 =
- Added Type option for Flip Image shortcode
- Added Centered Type option value for Tab shortcode
- Added Button Text option for Section Title shortcode
- Added Button Custom Link option for Section Title shortcode
- Added Button Type option for Section Title shortcode
- Added Button Size option for Section Title shortcode
- Added Button Font Size option for Section Title shortcode
- Added Button Color option for Section Title shortcode
- Added Button Hover Color option for Section Title shortcode
- Added Button Background Color option for Section Title shortcode
- Added Button Hover Background Color option for Section Title shortcode
- Added Button Border Color option for Section Title shortcode
- Added Button Hover Border Color option for Section Title shortcode
- Added Button Border Width option for Section Title shortcode
- Added Button Line Color option for Section Title shortcode
- Added Button Switch Line Color option for Section Title shortcode
- Added Button Margin option for Section Title shortcode
- Added Letter Margin option for Dropcaps shortcode
- Added Font Size option for Custom Font shortcode for 1366px screen size
- Added Line Height option for Custom Font shortcode for 1366px screen size
- Added Circle Fade Out Image Behavior option value for few shortcodes
- Improved framework functions
- Updated language pot file
- Fixed plugin global option link in admin toolbar

= 1.5.2 =
- Added Animation Type option for Tabs shortcode
- Added Items Hover Animation option for Clients shortcode
- Added Target option for Clients shortcode
- Updated language pot file

= 1.5.1 =
- Fixed internal server issue with get_current_screen function

= 1.5 =
- Added Clients Custom Post Type
- Added Clients shortcode
- Added Separator Width option for Section Title shortcode
- Added Separator Thickness option for Section Title shortcode
- Improved framework meta box types with image type
- Improved framework shortcode functionality
- Updated language pot file

= 1.4.4 =
- Added Letter Font Size option for Dropcaps shortcode
- Added Letter Line Height option for Dropcaps shortcode
- Added Letter Font Weight option for Dropcaps shortcode
- Added paragraph value for Title Tag option for Icon With Text shortcode
- Added paragraph value for Title Tag option for Image With Text shortcode
- Added paragraph value for Title Tag option for Interactive Banner shortcode
- Added Enable Separator option for Section Title shortcode
- Added Separator Color option for Section Title shortcode
- Added Separator Top Margin option for Section Title shortcode
- Added paragraph value for Title Tag option for Process shortcode
- Added paragraph value for Title Tag option for Process 2 shortcode
- Added Title Top Margin option for Process 2 shortcode
- Updated language pot file
- Fixed default title margin style for Section Title shortcode
- Fixed default text margin style for Section Title shortcode

= 1.4.3 =
- Added Slide From Bottom type option for Interactive Banner shortcode
- Added Shutter In Vertical type option for Interactive Banner shortcode
- Added Canvas Space Color option for Doughnut Chart shortcode
- Added Canvas Space Hover Color option for Doughnut Chart shortcode
- Added Canvas Space Color option for Pie Chart shortcode
- Added Canvas Space Hover Color option for Pie Chart shortcode
- Added Text Tag option for Section Title shortcode
- Updated language pot file
- Renamed Settings label to Options for all shortcodes group options

= 1.4.2 =
- Added Type option for Interactive Banner shortcode
- Added Text option for Interactive Banner shortcode
- Added Text Color option for Interactive Banner shortcode
- Added Text Top Margin option for Interactive Banner shortcode
- Added Type option for Gallery Block shortcode
- Updated language pot file
- Fixed Image Size option for Gallery Block shortcode

= 1.4.1 =
- Added Bar Height option for Progress Bar shortcode
- Added Title Margin Bottom option for Progress Bar shortcode
- Added Circle Number Color option for Process shortcode
- Added Circle Background Color option for Process shortcode
- Added Line Color option for Process shortcode
- Added Slider Navigation Skin option for Image Gallery shortcode
- Added Slider Navigation Skin option for Testimonials shortcode
- Updated language pot file

= 1.4 =
- Added Text Marquee shortcode
- Added Simple type for Tabs shortcode
- Added Skin option for Tabs shortcode
- Added Custom Link option for Full Screen Sections shortcode
- Improved shortcode parent constructor
- Improved shortcode helper functions
- Updated language pot file
- Redesigned logo and graphics

= 1.3 =
- Added Full Screen Sections shortcode
- Added Tabs shortcode
- Updated language pot file
- Fixed minor bugs

= 1.2.2 =
- Added With Icon type option for Blockquote shortcode
- Added Font Size option for Blockquote shortcode
- Added Line Height option for Blockquote shortcode
- Added Digit Font Size option for Counter shortcode
- Added Digit Line Height option for Counter shortcode
- Added Digit Font Weight option for Counter shortcode
- Added Text Color option for Dropcaps shortcode
- Added Image Behavior - Top Moving option for several shortcodes
- Updated language pot file

= 1.2.1 =
- Added Enable Legend option for Doughnut Chart shortcode
- Added Legend Position option for Doughnut Chart shortcode
- Added Enable Legend option for Pie Chart shortcode
- Added Legend Position option for Pie Chart shortcode
- Added Text Alignment option for Section Title shortcode
- Added Text Color option for Blockquote shortcode
- Added Icon Right Padding option for Icon List shortcode
- Added Space Between Items option for Icon List shortcode
- Updated language pot file

= 1.2 =
- Added Doughnut Chart shortcode
- Added Pie Chart shortcode
- Updated language pot file

= 1.1 =
- Added Flip Image shortcode
- Added Icon Progress Bar shortcode
- Improved shortcodes funtionalities
- Updated language pot file
- Fixed minor bugs

= 1.0 =
- Initial release.
