# Copyright (C) 2023 N<PERSON><PERSON>
# This file is distributed under the GPLv2 or later.
msgid ""
msgstr ""
"Project-Id-Version: Extensive VC Addons 1.9\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/trunk\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2023-01-11T10:00:52+01:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: extensive-vc\n"

#. Plugin Name of the plugin
#: lib/framework/framework-functions.php:14
msgid "Extensive VC Addons"
msgstr ""

#. Plugin URI of the plugin
msgid "http://wprealize.com/"
msgstr ""

#. Description of the plugin
msgid "WordPress plugin which allows you to add unique, flexible and fully responsive shortcodes. It is an addon for premium plugin WPBakery page builder."
msgstr ""

#. Author of the plugin
msgid "Nenad Obradovic"
msgstr ""

#. Author URI of the plugin
msgid "http://wprealize.com/introduced/"
msgstr ""

#: extensive-vc.php:184
#: lib/framework/framework-functions.php:81
msgid "Extensive VC Settings"
msgstr ""

#: extensive-vc.php:186
#: lib/framework/framework-functions.php:64
#: lib/framework/framework-functions.php:65
msgid "Settings"
msgstr ""

#: extensive-vc.php:214
msgid "The"
msgstr ""

#: extensive-vc.php:215
msgid " Extensive Addons"
msgstr ""

#: extensive-vc.php:216
msgid " plugin requires WPBakery page builder plugin. Please installed/activated it."
msgstr ""

#: lib/framework/framework-class.php:357
msgid "Upload Image"
msgstr ""

#: lib/framework/framework-class.php:358
msgid "Remove Image"
msgstr ""

#: lib/framework/framework-functions.php:15
#: lib/framework/framework-functions.php:180
#: shortcodes/shortcodes-extends-class.php:317
msgid "Extensive VC"
msgstr ""

#: lib/framework/framework-functions.php:87
msgid "General"
msgstr ""

#: lib/framework/framework-functions.php:104
#: lib/framework/framework-functions.php:158
msgid "If you enjoy using Extensive VC plugin and find it useful, please consider making a donation. Your donation will help encourage and support the plugin's continued development and better user support. Thank you, WP Realize team."
msgstr ""

#: lib/framework/framework-functions.php:105
#: lib/framework/framework-functions.php:159
msgid "Donate"
msgstr ""

#: lib/framework/framework-functions.php:129
#: lib/framework/framework-functions.php:130
msgid "About"
msgstr ""

#: lib/framework/framework-functions.php:151
msgid "Welcome to Extensive VC Addons %s"
msgstr ""

#: lib/framework/framework-functions.php:152
msgid "Extensive VC Admin Logo"
msgstr ""

#: lib/framework/framework-functions.php:154
msgid "Thank you for installing Extensive VC Addons! Extensive VC is most powerful plugin addons for WPBakery page builder."
msgstr ""

#: lib/framework/framework-functions.php:156
msgid "Extensive VC Admin Image"
msgstr ""

#: lib/framework/framework-options.php:25
msgid "Predefined Style"
msgstr ""

#: lib/framework/framework-options.php:26
msgid "Enabling this option will set predefined styles for elements (typography, shortcodes, post types etc)"
msgstr ""

#: lib/framework/framework-options.php:36
msgid "Main Color"
msgstr ""

#: lib/framework/framework-options.php:37
msgid "Set main color. Default color is #00bbb3"
msgstr ""

#: lib/framework/framework-options.php:46
msgid "Disable Shortcodes"
msgstr ""

#: lib/framework/framework-options.php:47
msgid "Disable shortcodes which you will not use on your site to increase site performance"
msgstr ""

#: lib/framework/framework-options.php:57
msgid "Disable Widgets"
msgstr ""

#: lib/framework/framework-options.php:58
msgid "Disable widgets which you will not use on your site to increase site performance"
msgstr ""

#: lib/framework/framework-options.php:68
msgid "Disable Ionicons Font"
msgstr ""

#: lib/framework/framework-options.php:69
msgid "Disable Ionicons font pack if you don't want to use it to increase site performance"
msgstr ""

#: lib/helpers-functions.php:444
#: lib/helpers-functions.php:478
#: lib/helpers-functions.php:505
#: lib/helpers-functions.php:535
#: lib/helpers-functions.php:558
#: lib/helpers-functions.php:585
#: lib/helpers-functions.php:608
#: lib/helpers-functions.php:638
#: lib/helpers-functions.php:667
#: lib/helpers-functions.php:692
#: lib/helpers-functions.php:718
#: lib/helpers-functions.php:765
#: plugins/contact-form-7/cf7-functions.php:31
#: plugins/woocommerce/shortcodes/product-list/product-list.php:100
#: plugins/woocommerce/shortcodes/product-list/product-list.php:118
#: post-types/clients/shortcodes/clients.php:258
#: post-types/testimonials/shortcodes/testimonials.php:165
#: shortcodes/blog-list/blog-list.php:83
#: shortcodes/blog-list/blog-list.php:139
#: shortcodes/blog-list/widgets/blog-list-widget.php:63
#: shortcodes/blog-list/widgets/blog-list-widget.php:109
#: shortcodes/button/button.php:220
#: shortcodes/button/widgets/button-widget.php:186
#: shortcodes/custom-font/custom-font.php:134
#: shortcodes/custom-font/widgets/custom-font-widget.php:110
#: shortcodes/full-screen-sections/full-screen-sections-item.php:89
#: shortcodes/full-screen-sections/full-screen-sections-item.php:100
#: shortcodes/image-gallery/image-gallery.php:214
#: shortcodes/post-carousel/post-carousel.php:113
#: shortcodes/post-carousel/post-carousel.php:235
#: shortcodes/section-title/section-title.php:61
#: shortcodes/separator/separator.php:80
#: shortcodes/separator/widgets/separator-widget.php:60
#: shortcodes/tabs/tabs.php:90
#: shortcodes/tabs/tabs.php:101
msgid "Default"
msgstr ""

#: lib/helpers-functions.php:447
#: post-types/clients/shortcodes/clients.php:179
#: shortcodes/image-gallery/image-gallery.php:135
#: shortcodes/post-carousel/post-carousel.php:165
msgid "One"
msgstr ""

#: lib/helpers-functions.php:448
#: post-types/clients/shortcodes/clients.php:180
#: shortcodes/image-gallery/image-gallery.php:136
#: shortcodes/post-carousel/post-carousel.php:166
msgid "Two"
msgstr ""

#: lib/helpers-functions.php:449
#: post-types/clients/shortcodes/clients.php:181
#: shortcodes/image-gallery/image-gallery.php:137
#: shortcodes/post-carousel/post-carousel.php:167
msgid "Three"
msgstr ""

#: lib/helpers-functions.php:450
#: post-types/clients/shortcodes/clients.php:182
#: shortcodes/image-gallery/image-gallery.php:138
#: shortcodes/post-carousel/post-carousel.php:168
msgid "Four"
msgstr ""

#: lib/helpers-functions.php:451
#: post-types/clients/shortcodes/clients.php:183
#: shortcodes/image-gallery/image-gallery.php:139
#: shortcodes/post-carousel/post-carousel.php:169
msgid "Five"
msgstr ""

#: lib/helpers-functions.php:452
#: post-types/clients/shortcodes/clients.php:184
#: shortcodes/image-gallery/image-gallery.php:140
#: shortcodes/post-carousel/post-carousel.php:170
msgid "Six"
msgstr ""

#: lib/helpers-functions.php:481
msgid "Large (50px)"
msgstr ""

#: lib/helpers-functions.php:482
msgid "Medium (40px)"
msgstr ""

#: lib/helpers-functions.php:483
msgid "Normal (30px)"
msgstr ""

#: lib/helpers-functions.php:484
msgid "Small (20px)"
msgstr ""

#: lib/helpers-functions.php:485
msgid "Tiny (10px)"
msgstr ""

#: lib/helpers-functions.php:486
#: lib/helpers-functions.php:563
#: lib/helpers-functions.php:565
msgid "No"
msgstr ""

#: lib/helpers-functions.php:508
msgid "Date"
msgstr ""

#: lib/helpers-functions.php:509
#: plugins/woocommerce/shortcodes/product-list/product-list.php:103
msgid "ID"
msgstr ""

#: lib/helpers-functions.php:510
msgid "Menu Order"
msgstr ""

#: lib/helpers-functions.php:511
msgid "Post Name"
msgstr ""

#: lib/helpers-functions.php:512
msgid "Random"
msgstr ""

#: lib/helpers-functions.php:513
#: shortcodes/counter/counter.php:105
#: shortcodes/custom-font/custom-font.php:58
#: shortcodes/custom-font/widgets/custom-font-widget.php:42
#: shortcodes/flip-image/flip-image.php:91
#: shortcodes/icon-progress-bar/icon-progress-bar.php:96
#: shortcodes/icon-with-text/icon-with-text.php:91
#: shortcodes/image-with-text/image-with-text.php:81
#: shortcodes/interactive-banner/interactive-banner.php:113
#: shortcodes/pricing-table/pricing-table-item.php:60
#: shortcodes/process-2/process-2-item.php:70
#: shortcodes/process/process-item.php:59
#: shortcodes/progress-bar/progress-bar.php:93
#: shortcodes/section-title/section-title.php:70
#: shortcodes/tabs/tabs-item.php:59
msgid "Title"
msgstr ""

#: lib/helpers-functions.php:538
msgid "Ascending"
msgstr ""

#: lib/helpers-functions.php:539
msgid "Descending"
msgstr ""

#: lib/helpers-functions.php:562
#: lib/helpers-functions.php:566
msgid "Yes"
msgstr ""

#: lib/helpers-functions.php:588
msgid "Same Window"
msgstr ""

#: lib/helpers-functions.php:589
msgid "New Window"
msgstr ""

#: lib/helpers-functions.php:641
msgid "100 Thin"
msgstr ""

#: lib/helpers-functions.php:642
msgid "200 Thin-Light"
msgstr ""

#: lib/helpers-functions.php:643
msgid "300 Light"
msgstr ""

#: lib/helpers-functions.php:644
msgid "400 Normal"
msgstr ""

#: lib/helpers-functions.php:645
msgid "500 Medium"
msgstr ""

#: lib/helpers-functions.php:646
msgid "600 Semi-Bold"
msgstr ""

#: lib/helpers-functions.php:647
msgid "700 Bold"
msgstr ""

#: lib/helpers-functions.php:648
msgid "800 Extra-Bold"
msgstr ""

#: lib/helpers-functions.php:649
msgid "900 Ultra-Bold"
msgstr ""

#: lib/helpers-functions.php:670
#: lib/helpers-functions.php:927
#: shortcodes/button/button.php:80
#: shortcodes/button/widgets/button-widget.php:63
msgid "Normal"
msgstr ""

#: lib/helpers-functions.php:671
msgid "Italic"
msgstr ""

#: lib/helpers-functions.php:672
msgid "Oblique"
msgstr ""

#: lib/helpers-functions.php:673
#: lib/helpers-functions.php:699
#: lib/helpers-functions.php:725
msgid "Initial"
msgstr ""

#: lib/helpers-functions.php:674
#: lib/helpers-functions.php:700
#: lib/helpers-functions.php:726
#: shortcodes/full-screen-sections/full-screen-sections-item.php:80
msgid "Inherit"
msgstr ""

#: lib/helpers-functions.php:695
#: lib/helpers-functions.php:721
#: lib/helpers-functions.php:741
msgid "None"
msgstr ""

#: lib/helpers-functions.php:696
msgid "Capitalize"
msgstr ""

#: lib/helpers-functions.php:697
msgid "Uppercase"
msgstr ""

#: lib/helpers-functions.php:698
msgid "Lowercase"
msgstr ""

#: lib/helpers-functions.php:722
msgid "Underline"
msgstr ""

#: lib/helpers-functions.php:723
msgid "Overline"
msgstr ""

#: lib/helpers-functions.php:724
msgid "Line-Through"
msgstr ""

#: lib/helpers-functions.php:742
msgid "Overlay"
msgstr ""

#: lib/helpers-functions.php:743
msgid "Zoom"
msgstr ""

#: lib/helpers-functions.php:744
msgid "Lightbox"
msgstr ""

#: lib/helpers-functions.php:745
msgid "Top Moving"
msgstr ""

#: lib/helpers-functions.php:746
msgid "Circle Fade Out"
msgstr ""

#: lib/helpers-functions.php:747
#: shortcodes/interactive-banner/interactive-banner.php:63
msgid "Bordered"
msgstr ""

#: lib/helpers-functions.php:768
msgid "Font Awesome"
msgstr ""

#: lib/helpers-functions.php:769
msgid "Open Iconic"
msgstr ""

#: lib/helpers-functions.php:770
msgid "Typicons"
msgstr ""

#: lib/helpers-functions.php:771
msgid "Entypo"
msgstr ""

#: lib/helpers-functions.php:772
msgid "Linecons"
msgstr ""

#: lib/helpers-functions.php:773
msgid "Mono Social"
msgstr ""

#: lib/helpers-functions.php:774
msgid "Material"
msgstr ""

#: lib/helpers-functions.php:792
msgid "Icon Library"
msgstr ""

#: lib/helpers-functions.php:794
msgid "Choose icon library"
msgstr ""

#: lib/helpers-functions.php:799
#: lib/helpers-functions.php:811
#: lib/helpers-functions.php:823
#: lib/helpers-functions.php:835
#: lib/helpers-functions.php:847
#: lib/helpers-functions.php:859
#: lib/helpers-functions.php:871
msgid "Icon"
msgstr ""

#: lib/helpers-functions.php:800
#: lib/helpers-functions.php:812
#: lib/helpers-functions.php:824
#: lib/helpers-functions.php:836
#: lib/helpers-functions.php:848
#: lib/helpers-functions.php:860
#: lib/helpers-functions.php:872
msgid "Select icon from library"
msgstr ""

#: lib/helpers-functions.php:900
msgid "Button Custom Link"
msgstr ""

#: lib/helpers-functions.php:906
#: post-types/clients/shortcodes/clients.php:77
#: shortcodes/blockquote/blockquote.php:58
#: shortcodes/blog-list/blog-list.php:69
#: shortcodes/blog-list/widgets/blog-list-widget.php:48
#: shortcodes/button/button.php:59
#: shortcodes/button/widgets/button-widget.php:42
#: shortcodes/dropcaps/dropcaps.php:58
#: shortcodes/flip-image/flip-image.php:58
#: shortcodes/gallery-block/gallery-block.php:58
#: shortcodes/icon-with-text/icon-with-text.php:59
#: shortcodes/image-gallery/image-gallery.php:74
#: shortcodes/interactive-banner/interactive-banner.php:59
#: shortcodes/post-carousel/post-carousel.php:78
#: shortcodes/progress-bar/progress-bar.php:70
#: shortcodes/tabs/tabs.php:75
msgid "Type"
msgstr ""

#: lib/helpers-functions.php:908
#: shortcodes/button/button.php:61
#: shortcodes/button/widgets/button-widget.php:44
#: shortcodes/separator/separator.php:82
#: shortcodes/separator/widgets/separator-widget.php:62
msgid "Solid"
msgstr ""

#: lib/helpers-functions.php:909
#: shortcodes/button/button.php:62
#: shortcodes/button/widgets/button-widget.php:45
msgid "Outline"
msgstr ""

#: lib/helpers-functions.php:910
#: shortcodes/blockquote/blockquote.php:60
#: shortcodes/blog-list/blog-list.php:73
#: shortcodes/blog-list/widgets/blog-list-widget.php:52
#: shortcodes/button/button.php:63
#: shortcodes/button/widgets/button-widget.php:46
#: shortcodes/dropcaps/dropcaps.php:60
#: shortcodes/tabs/tabs.php:78
msgid "Simple"
msgstr ""

#: lib/helpers-functions.php:911
#: shortcodes/button/button.php:64
#: shortcodes/button/widgets/button-widget.php:47
msgid "Simple Fill Line On Hover"
msgstr ""

#: lib/helpers-functions.php:912
#: shortcodes/button/button.php:65
#: shortcodes/button/widgets/button-widget.php:48
msgid "Simple Fill Text On Hover"
msgstr ""

#: lib/helpers-functions.php:913
#: shortcodes/button/button.php:66
#: shortcodes/button/widgets/button-widget.php:49
msgid "Simple Strike Line On Hover"
msgstr ""

#: lib/helpers-functions.php:914
#: shortcodes/button/button.php:68
#: shortcodes/button/widgets/button-widget.php:51
msgid "Simple Switch Line On Hover"
msgstr ""

#: lib/helpers-functions.php:918
#: lib/helpers-functions.php:933
#: lib/helpers-functions.php:940
#: lib/helpers-functions.php:947
#: lib/helpers-functions.php:954
#: lib/helpers-functions.php:961
#: lib/helpers-functions.php:968
#: lib/helpers-functions.php:975
#: lib/helpers-functions.php:982
#: lib/helpers-functions.php:989
#: lib/helpers-functions.php:996
#: lib/helpers-functions.php:1003
#: lib/helpers-functions.php:1011
#: shortcodes/blog-list/blog-list.php:208
msgid "Button Options"
msgstr ""

#: lib/helpers-functions.php:923
#: shortcodes/button/button.php:76
#: shortcodes/button/widgets/button-widget.php:58
msgid "Size"
msgstr ""

#: lib/helpers-functions.php:925
#: plugins/woocommerce/shortcodes/product-list/product-list.php:121
#: shortcodes/blog-list/blog-list.php:150
#: shortcodes/blog-list/widgets/blog-list-widget.php:121
#: shortcodes/button/button.php:78
#: shortcodes/button/widgets/button-widget.php:61
#: shortcodes/post-carousel/post-carousel.php:124
msgid "Large"
msgstr ""

#: lib/helpers-functions.php:926
#: plugins/woocommerce/shortcodes/product-list/product-list.php:120
#: shortcodes/blog-list/blog-list.php:151
#: shortcodes/blog-list/widgets/blog-list-widget.php:122
#: shortcodes/button/button.php:79
#: shortcodes/button/widgets/button-widget.php:62
#: shortcodes/post-carousel/post-carousel.php:125
msgid "Medium"
msgstr ""

#: lib/helpers-functions.php:928
#: shortcodes/button/button.php:81
#: shortcodes/button/widgets/button-widget.php:64
msgid "Small"
msgstr ""

#: lib/helpers-functions.php:929
#: shortcodes/button/button.php:82
#: shortcodes/button/widgets/button-widget.php:65
msgid "Tiny"
msgstr ""

#: lib/helpers-functions.php:938
#: shortcodes/blockquote/blockquote.php:81
#: shortcodes/button/button.php:111
#: shortcodes/button/widgets/button-widget.php:93
#: shortcodes/custom-font/custom-font.php:86
#: shortcodes/custom-font/custom-font.php:145
#: shortcodes/custom-font/custom-font.php:159
#: shortcodes/custom-font/custom-font.php:173
#: shortcodes/custom-font/custom-font.php:187
#: shortcodes/custom-font/custom-font.php:201
#: shortcodes/custom-font/custom-font.php:215
#: shortcodes/custom-font/widgets/custom-font-widget.php:69
#: shortcodes/custom-font/widgets/custom-font-widget.php:121
#: shortcodes/custom-font/widgets/custom-font-widget.php:133
#: shortcodes/custom-font/widgets/custom-font-widget.php:145
#: shortcodes/custom-font/widgets/custom-font-widget.php:157
#: shortcodes/custom-font/widgets/custom-font-widget.php:169
#: shortcodes/custom-font/widgets/custom-font-widget.php:181
#: shortcodes/text-marquee/text-marquee.php:69
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:52
msgid "Font Size (px or em)"
msgstr ""

#: lib/helpers-functions.php:945
#: shortcodes/button/button.php:150
#: shortcodes/button/widgets/button-widget.php:126
#: shortcodes/custom-font/custom-font.php:69
#: shortcodes/custom-font/widgets/custom-font-widget.php:53
#: shortcodes/doughnut-chart/doughnut-chart-item.php:63
#: shortcodes/pie-chart/pie-chart-item.php:63
#: shortcodes/separator/separator.php:101
#: shortcodes/separator/widgets/separator-widget.php:80
#: shortcodes/svg-text/svg-text.php:68
#: shortcodes/text-marquee/text-marquee.php:64
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:47
msgid "Color"
msgstr ""

#: lib/helpers-functions.php:952
#: shortcodes/button/button.php:156
#: shortcodes/button/widgets/button-widget.php:131
msgid "Hover Color"
msgstr ""

#: lib/helpers-functions.php:959
#: shortcodes/button/button.php:162
#: shortcodes/button/widgets/button-widget.php:136
#: shortcodes/full-screen-sections/full-screen-sections-item.php:59
#: shortcodes/pricing-table/pricing-table-item.php:167
msgid "Background Color"
msgstr ""

#: lib/helpers-functions.php:966
#: shortcodes/button/button.php:169
#: shortcodes/button/widgets/button-widget.php:142
msgid "Hover Background Color"
msgstr ""

#: lib/helpers-functions.php:973
#: shortcodes/button/button.php:176
#: shortcodes/button/widgets/button-widget.php:148
#: shortcodes/pricing-table/pricing-table-item.php:173
msgid "Border Color"
msgstr ""

#: lib/helpers-functions.php:980
#: shortcodes/button/button.php:183
#: shortcodes/button/widgets/button-widget.php:154
msgid "Hover Border Color"
msgstr ""

#: lib/helpers-functions.php:987
#: shortcodes/button/button.php:190
#: shortcodes/button/widgets/button-widget.php:160
msgid "Border Width (px)"
msgstr ""

#: lib/helpers-functions.php:994
#: shortcodes/button/button.php:197
#: shortcodes/button/widgets/button-widget.php:166
#: shortcodes/line-graph/line-graph.php:88
#: shortcodes/process/process.php:108
msgid "Line Color"
msgstr ""

#: lib/helpers-functions.php:1001
#: shortcodes/button/button.php:204
#: shortcodes/button/widgets/button-widget.php:172
msgid "Switch Line Color"
msgstr ""

#: lib/helpers-functions.php:1008
#: shortcodes/button/button.php:211
#: shortcodes/button/widgets/button-widget.php:178
msgid "Margin"
msgstr ""

#: lib/helpers-functions.php:1009
#: shortcodes/button/button.php:212
#: shortcodes/button/widgets/button-widget.php:179
#: shortcodes/custom-font/custom-font.php:75
#: shortcodes/custom-font/widgets/custom-font-widget.php:59
#: shortcodes/dropcaps/dropcaps.php:108
msgid "Insert margin in format: top right bottom left (e.g. 10px 5px 10px 5px)"
msgstr ""

#: plugins/contact-form-7/cf7-functions.php:39
msgid "No contact forms 7 found"
msgstr ""

#: plugins/contact-form-7/widgets/cf7-widget.php:16
msgid "EVC Contact Form 7"
msgstr ""

#: plugins/contact-form-7/widgets/cf7-widget.php:17
msgid "Add contact form 7 element to widget areas"
msgstr ""

#: plugins/contact-form-7/widgets/cf7-widget.php:31
#: plugins/woocommerce/shortcodes/product-list/product-list.php:52
#: post-types/clients/shortcodes/clients.php:71
#: post-types/testimonials/shortcodes/testimonials.php:71
#: shortcodes/blockquote/blockquote.php:52
#: shortcodes/blog-list/blog-list.php:63
#: shortcodes/blog-list/widgets/blog-list-widget.php:31
#: shortcodes/blog-list/widgets/blog-list-widget.php:42
#: shortcodes/button/button.php:53
#: shortcodes/button/widgets/button-widget.php:31
#: shortcodes/counter/counter.php:64
#: shortcodes/custom-font/custom-font.php:52
#: shortcodes/custom-font/widgets/custom-font-widget.php:31
#: shortcodes/doughnut-chart/doughnut-chart.php:77
#: shortcodes/dropcaps/dropcaps.php:52
#: shortcodes/flip-image/flip-image.php:52
#: shortcodes/full-screen-sections/full-screen-sections-item.php:53
#: shortcodes/full-screen-sections/full-screen-sections.php:77
#: shortcodes/gallery-block/gallery-block.php:52
#: shortcodes/icon-list/icon-list-item.php:54
#: shortcodes/icon-list/icon-list.php:71
#: shortcodes/icon-progress-bar/icon-progress-bar.php:53
#: shortcodes/icon-with-text/icon-with-text.php:53
#: shortcodes/image-gallery/image-gallery.php:68
#: shortcodes/image-with-text/image-with-text.php:52
#: shortcodes/interactive-banner/interactive-banner.php:53
#: shortcodes/line-graph/line-graph.php:77
#: shortcodes/pie-chart/pie-chart.php:77
#: shortcodes/post-carousel/post-carousel.php:72
#: shortcodes/pricing-table/pricing-table-item.php:54
#: shortcodes/pricing-table/pricing-table.php:69
#: shortcodes/process-2/process-2-item.php:53
#: shortcodes/process-2/process-2.php:69
#: shortcodes/process/process-item.php:53
#: shortcodes/process/process.php:69
#: shortcodes/progress-bar/progress-bar.php:64
#: shortcodes/section-title/section-title.php:53
#: shortcodes/separator/separator.php:52
#: shortcodes/separator/widgets/separator-widget.php:31
#: shortcodes/single-image/single-image.php:52
#: shortcodes/svg-text/svg-text.php:52
#: shortcodes/tabs/tabs-item.php:53
#: shortcodes/tabs/tabs.php:69
#: shortcodes/text-marquee/text-marquee.php:52
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:31
msgid "Custom CSS Class"
msgstr ""

#: plugins/contact-form-7/widgets/cf7-widget.php:32
#: plugins/woocommerce/shortcodes/product-list/product-list.php:53
#: post-types/clients/shortcodes/clients.php:72
#: post-types/testimonials/shortcodes/testimonials.php:72
#: shortcodes/blockquote/blockquote.php:53
#: shortcodes/blog-list/blog-list.php:64
#: shortcodes/blog-list/widgets/blog-list-widget.php:32
#: shortcodes/blog-list/widgets/blog-list-widget.php:43
#: shortcodes/button/button.php:54
#: shortcodes/button/widgets/button-widget.php:32
#: shortcodes/counter/counter.php:65
#: shortcodes/custom-font/custom-font.php:53
#: shortcodes/custom-font/widgets/custom-font-widget.php:32
#: shortcodes/doughnut-chart/doughnut-chart.php:78
#: shortcodes/dropcaps/dropcaps.php:53
#: shortcodes/flip-image/flip-image.php:53
#: shortcodes/full-screen-sections/full-screen-sections-item.php:54
#: shortcodes/full-screen-sections/full-screen-sections.php:78
#: shortcodes/gallery-block/gallery-block.php:53
#: shortcodes/icon-list/icon-list-item.php:55
#: shortcodes/icon-list/icon-list.php:72
#: shortcodes/icon-progress-bar/icon-progress-bar.php:54
#: shortcodes/icon-with-text/icon-with-text.php:54
#: shortcodes/image-gallery/image-gallery.php:69
#: shortcodes/image-with-text/image-with-text.php:53
#: shortcodes/interactive-banner/interactive-banner.php:54
#: shortcodes/line-graph/line-graph.php:78
#: shortcodes/pie-chart/pie-chart.php:78
#: shortcodes/post-carousel/post-carousel.php:73
#: shortcodes/pricing-table/pricing-table-item.php:55
#: shortcodes/pricing-table/pricing-table.php:70
#: shortcodes/process-2/process-2-item.php:54
#: shortcodes/process-2/process-2.php:70
#: shortcodes/process/process-item.php:54
#: shortcodes/process/process.php:70
#: shortcodes/progress-bar/progress-bar.php:65
#: shortcodes/section-title/section-title.php:54
#: shortcodes/separator/separator.php:53
#: shortcodes/separator/widgets/separator-widget.php:32
#: shortcodes/single-image/single-image.php:53
#: shortcodes/svg-text/svg-text.php:53
#: shortcodes/tabs/tabs-item.php:54
#: shortcodes/tabs/tabs.php:70
#: shortcodes/text-marquee/text-marquee.php:53
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:32
msgid "Style particular content element differently - add a class name and refer to it in custom CSS"
msgstr ""

#: plugins/contact-form-7/widgets/cf7-widget.php:37
#: shortcodes/blog-list/widgets/blog-list-widget.php:37
#: shortcodes/button/widgets/button-widget.php:37
#: shortcodes/custom-font/widgets/custom-font-widget.php:37
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:37
msgid "Widget Title"
msgstr ""

#: plugins/contact-form-7/widgets/cf7-widget.php:42
msgid "Select Contact Form 7"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:24
msgid "Product List"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:58
#: shortcodes/blog-list/blog-list.php:81
#: shortcodes/blog-list/widgets/blog-list-widget.php:60
msgid "Layout Collections"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:60
#: shortcodes/blog-list/blog-list.php:71
#: shortcodes/blog-list/widgets/blog-list-widget.php:50
#: shortcodes/tabs/tabs.php:77
msgid "Standard"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:61
msgid "Standard - Button Sliding"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:62
#: post-types/clients/shortcodes/clients.php:80
#: shortcodes/blog-list/blog-list.php:72
#: shortcodes/blog-list/widgets/blog-list-widget.php:51
msgid "Gallery"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:69
msgid "Number of Products"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:74
#: post-types/clients/shortcodes/clients.php:161
#: shortcodes/blog-list/blog-list.php:98
#: shortcodes/blog-list/widgets/blog-list-widget.php:77
#: shortcodes/image-gallery/image-gallery.php:117
#: shortcodes/pricing-table/pricing-table.php:75
#: shortcodes/process-2/process-2.php:75
#: shortcodes/process/process.php:75
msgid "Number of Columns"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:80
#: post-types/clients/shortcodes/clients.php:169
#: shortcodes/blog-list/blog-list.php:104
#: shortcodes/blog-list/widgets/blog-list-widget.php:83
#: shortcodes/gallery-block/gallery-block.php:68
#: shortcodes/image-gallery/image-gallery.php:125
#: shortcodes/pricing-table/pricing-table.php:81
msgid "Space Between Items"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:86
#: post-types/clients/shortcodes/clients.php:99
#: post-types/testimonials/shortcodes/testimonials.php:89
#: shortcodes/blog-list/blog-list.php:116
#: shortcodes/blog-list/widgets/blog-list-widget.php:95
#: shortcodes/post-carousel/post-carousel.php:99
msgid "Order By"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:87
msgid "On Sale"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:92
#: post-types/clients/shortcodes/clients.php:105
#: post-types/testimonials/shortcodes/testimonials.php:95
#: shortcodes/blog-list/blog-list.php:122
#: shortcodes/blog-list/widgets/blog-list-widget.php:101
#: shortcodes/post-carousel/post-carousel.php:105
msgid "Order"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:98
msgid "Choose Sorting Taxonomy"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:101
#: post-types/clients/shortcodes/clients.php:93
#: post-types/clients/shortcodes/clients.php:411
#: post-types/clients/shortcodes/clients.php:441
#: post-types/testimonials/shortcodes/testimonials.php:83
#: post-types/testimonials/shortcodes/testimonials.php:297
#: post-types/testimonials/shortcodes/testimonials.php:327
#: shortcodes/blog-list/blog-list.php:110
#: shortcodes/blog-list/blog-list.php:315
#: shortcodes/blog-list/blog-list.php:343
#: shortcodes/blog-list/widgets/blog-list-widget.php:89
#: shortcodes/post-carousel/post-carousel.php:93
#: shortcodes/post-carousel/post-carousel.php:357
#: shortcodes/post-carousel/post-carousel.php:385
msgid "Category"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:102
msgid "Tag"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:105
msgid "If you would like to display only certain products, this is where you can select the criteria to choose which products to display"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:110
msgid "Enter Taxonomy Values"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:111
msgid "Separate values (category slugs, tags, or post IDs) with a comma"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:116
#: shortcodes/blog-list/blog-list.php:147
#: shortcodes/blog-list/widgets/blog-list-widget.php:117
#: shortcodes/post-carousel/post-carousel.php:121
msgid "Image Proportions"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:119
#: shortcodes/blog-list/blog-list.php:149
#: shortcodes/blog-list/widgets/blog-list-widget.php:120
#: shortcodes/post-carousel/post-carousel.php:123
msgid "Original"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:122
msgid "Shop Single"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:123
msgid "Shop Thumbnail"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:129
msgid "Enable Title"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:131
#: plugins/woocommerce/shortcodes/product-list/product-list.php:139
#: plugins/woocommerce/shortcodes/product-list/product-list.php:146
#: plugins/woocommerce/shortcodes/product-list/product-list.php:153
#: plugins/woocommerce/shortcodes/product-list/product-list.php:160
#: shortcodes/blog-list/blog-list.php:142
#: shortcodes/blog-list/blog-list.php:155
#: shortcodes/blog-list/blog-list.php:162
#: shortcodes/blog-list/blog-list.php:170
#: shortcodes/blog-list/blog-list.php:178
#: shortcodes/blog-list/blog-list.php:186
#: shortcodes/blog-list/blog-list.php:194
#: shortcodes/blog-list/blog-list.php:201
#: shortcodes/button/button.php:151
#: shortcodes/button/button.php:157
#: shortcodes/button/button.php:164
#: shortcodes/button/button.php:171
#: shortcodes/button/button.php:178
#: shortcodes/button/button.php:185
#: shortcodes/button/button.php:192
#: shortcodes/button/button.php:199
#: shortcodes/button/button.php:206
#: shortcodes/button/button.php:213
#: shortcodes/button/button.php:225
#: shortcodes/doughnut-chart/doughnut-chart.php:102
#: shortcodes/doughnut-chart/doughnut-chart.php:108
#: shortcodes/doughnut-chart/doughnut-chart.php:115
#: shortcodes/doughnut-chart/doughnut-chart.php:128
#: shortcodes/doughnut-chart/doughnut-chart.php:136
#: shortcodes/doughnut-chart/doughnut-chart.php:143
#: shortcodes/flip-image/flip-image.php:86
#: shortcodes/flip-image/flip-image.php:99
#: shortcodes/flip-image/flip-image.php:106
#: shortcodes/flip-image/flip-image.php:118
#: shortcodes/flip-image/flip-image.php:125
#: shortcodes/image-with-text/image-with-text.php:90
#: shortcodes/image-with-text/image-with-text.php:97
#: shortcodes/image-with-text/image-with-text.php:104
#: shortcodes/image-with-text/image-with-text.php:116
#: shortcodes/image-with-text/image-with-text.php:123
#: shortcodes/line-graph/line-graph.php:89
#: shortcodes/line-graph/line-graph.php:95
#: shortcodes/line-graph/line-graph.php:103
#: shortcodes/line-graph/line-graph.php:111
#: shortcodes/pie-chart/pie-chart.php:102
#: shortcodes/pie-chart/pie-chart.php:108
#: shortcodes/pie-chart/pie-chart.php:115
#: shortcodes/pie-chart/pie-chart.php:128
#: shortcodes/pie-chart/pie-chart.php:136
#: shortcodes/pie-chart/pie-chart.php:143
#: shortcodes/post-carousel/post-carousel.php:116
#: shortcodes/post-carousel/post-carousel.php:128
#: shortcodes/post-carousel/post-carousel.php:135
#: shortcodes/post-carousel/post-carousel.php:142
#: shortcodes/post-carousel/post-carousel.php:150
#: shortcodes/post-carousel/post-carousel.php:158
#: shortcodes/pricing-table/pricing-table-item.php:68
#: shortcodes/pricing-table/pricing-table-item.php:75
#: shortcodes/pricing-table/pricing-table-item.php:87
#: shortcodes/pricing-table/pricing-table-item.php:94
#: shortcodes/pricing-table/pricing-table-item.php:101
#: shortcodes/pricing-table/pricing-table-item.php:108
#: shortcodes/pricing-table/pricing-table-item.php:121
#: shortcodes/pricing-table/pricing-table-item.php:128
#: shortcodes/pricing-table/pricing-table-item.php:141
#: shortcodes/pricing-table/pricing-table-item.php:148
#: shortcodes/process-2/process-2-item.php:78
#: shortcodes/process-2/process-2-item.php:85
#: shortcodes/process-2/process-2-item.php:92
#: shortcodes/process-2/process-2-item.php:104
#: shortcodes/process-2/process-2-item.php:111
#: shortcodes/process/process-item.php:67
#: shortcodes/process/process-item.php:74
#: shortcodes/process/process-item.php:86
#: shortcodes/process/process-item.php:93
#: shortcodes/process/process.php:97
#: shortcodes/process/process.php:103
#: shortcodes/process/process.php:109
#: shortcodes/separator/separator.php:90
#: shortcodes/separator/separator.php:96
#: shortcodes/separator/separator.php:102
msgid "Design Options"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:136
#: post-types/clients/shortcodes/clients.php:132
#: shortcodes/blog-list/blog-list.php:160
#: shortcodes/blog-list/widgets/blog-list-widget.php:130
#: shortcodes/counter/counter.php:111
#: shortcodes/custom-font/custom-font.php:63
#: shortcodes/custom-font/widgets/custom-font-widget.php:47
#: shortcodes/flip-image/flip-image.php:96
#: shortcodes/icon-progress-bar/icon-progress-bar.php:102
#: shortcodes/icon-with-text/icon-with-text.php:97
#: shortcodes/image-with-text/image-with-text.php:87
#: shortcodes/interactive-banner/interactive-banner.php:119
#: shortcodes/post-carousel/post-carousel.php:133
#: shortcodes/pricing-table/pricing-table-item.php:65
#: shortcodes/process-2/process-2-item.php:75
#: shortcodes/process/process-item.php:64
#: shortcodes/progress-bar/progress-bar.php:99
#: shortcodes/section-title/section-title.php:76
msgid "Title Tag"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:144
msgid "Enable Ratings"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:151
msgid "Enable Price"
msgstr ""

#: plugins/woocommerce/shortcodes/product-list/product-list.php:158
#: shortcodes/blog-list/blog-list.php:183
#: shortcodes/blog-list/widgets/blog-list-widget.php:149
msgid "Enable Category"
msgstr ""

#: post-types/clients/admin/meta-boxes/clients-meta-boxes.php:26
#: post-types/clients/shortcodes/clients.php:24
msgid "Clients"
msgstr ""

#: post-types/clients/admin/meta-boxes/clients-meta-boxes.php:80
#: shortcodes/flip-image/flip-image.php:68
#: shortcodes/image-with-text/image-with-text.php:58
#: shortcodes/interactive-banner/interactive-banner.php:72
#: shortcodes/process-2/process-2-item.php:59
#: shortcodes/single-image/single-image.php:58
msgid "Image"
msgstr ""

#: post-types/clients/admin/meta-boxes/clients-meta-boxes.php:89
msgid "Hover Image"
msgstr ""

#: post-types/clients/admin/meta-boxes/clients-meta-boxes.php:98
msgid "Link"
msgstr ""

#: post-types/clients/clients-class.php:61
#: post-types/clients/clients-class.php:62
msgid "EVC Clients"
msgstr ""

#: post-types/clients/clients-class.php:63
msgid "Client"
msgstr ""

#: post-types/clients/clients-class.php:64
msgid "New Client"
msgstr ""

#: post-types/clients/clients-class.php:65
msgid "Add New Client"
msgstr ""

#: post-types/clients/clients-class.php:66
msgid "Edit Client"
msgstr ""

#: post-types/clients/clients-class.php:86
#: post-types/clients/clients-class.php:96
msgid "Clients Categories"
msgstr ""

#: post-types/clients/clients-class.php:87
msgid "Client Category"
msgstr ""

#: post-types/clients/clients-class.php:88
msgid "Search Clients Categories"
msgstr ""

#: post-types/clients/clients-class.php:89
msgid "All Clients Categories"
msgstr ""

#: post-types/clients/clients-class.php:90
msgid "Parent Client Category"
msgstr ""

#: post-types/clients/clients-class.php:91
msgid "Parent Client Category:"
msgstr ""

#: post-types/clients/clients-class.php:92
msgid "Edit Clients Category"
msgstr ""

#: post-types/clients/clients-class.php:93
msgid "Update Clients Category"
msgstr ""

#: post-types/clients/clients-class.php:94
msgid "Add New Clients Category"
msgstr ""

#: post-types/clients/clients-class.php:95
msgid "New Clients Category Name"
msgstr ""

#: post-types/clients/shortcodes/clients.php:79
#: shortcodes/image-gallery/image-gallery.php:77
msgid "Slider"
msgstr ""

#: post-types/clients/shortcodes/clients.php:87
msgid "Number of Clients"
msgstr ""

#: post-types/clients/shortcodes/clients.php:88
msgid "Enter number of clients or leave empty for showing all clients"
msgstr ""

#: post-types/clients/shortcodes/clients.php:94
#: post-types/testimonials/shortcodes/testimonials.php:84
#: shortcodes/blog-list/blog-list.php:111
#: shortcodes/blog-list/widgets/blog-list-widget.php:90
#: shortcodes/post-carousel/post-carousel.php:94
msgid "Enter one category slug or leave empty for showing all categories"
msgstr ""

#: post-types/clients/shortcodes/clients.php:111
#: shortcodes/button/widgets/button-widget.php:82
#: shortcodes/gallery-block/gallery-block.php:104
#: shortcodes/image-gallery/image-gallery.php:110
msgid "Custom Link Target"
msgstr ""

#: post-types/clients/shortcodes/clients.php:117
msgid "Items Hover Animation"
msgstr ""

#: post-types/clients/shortcodes/clients.php:119
msgid "Switch Images"
msgstr ""

#: post-types/clients/shortcodes/clients.php:120
msgid "Roll Over"
msgstr ""

#: post-types/clients/shortcodes/clients.php:126
msgid "Enable Title Text"
msgstr ""

#: post-types/clients/shortcodes/clients.php:135
#: post-types/clients/shortcodes/clients.php:142
#: post-types/clients/shortcodes/clients.php:149
#: post-types/clients/shortcodes/clients.php:156
#: shortcodes/counter/counter.php:114
#: shortcodes/counter/counter.php:121
#: shortcodes/counter/counter.php:128
#: shortcodes/icon-progress-bar/icon-progress-bar.php:105
#: shortcodes/icon-progress-bar/icon-progress-bar.php:112
#: shortcodes/icon-progress-bar/icon-progress-bar.php:119
#: shortcodes/icon-with-text/icon-with-text.php:101
#: shortcodes/icon-with-text/icon-with-text.php:108
#: shortcodes/interactive-banner/interactive-banner.php:122
#: shortcodes/interactive-banner/interactive-banner.php:129
#: shortcodes/progress-bar/progress-bar.php:102
#: shortcodes/progress-bar/progress-bar.php:109
#: shortcodes/progress-bar/progress-bar.php:116
#: shortcodes/section-title/section-title.php:79
#: shortcodes/section-title/section-title.php:86
msgid "Title Options"
msgstr ""

#: post-types/clients/shortcodes/clients.php:140
#: shortcodes/counter/counter.php:119
#: shortcodes/flip-image/flip-image.php:104
#: shortcodes/icon-progress-bar/icon-progress-bar.php:110
#: shortcodes/icon-with-text/icon-with-text.php:106
#: shortcodes/image-with-text/image-with-text.php:95
#: shortcodes/interactive-banner/interactive-banner.php:127
#: shortcodes/pricing-table/pricing-table-item.php:73
#: shortcodes/process-2/process-2-item.php:83
#: shortcodes/process/process-item.php:72
#: shortcodes/progress-bar/progress-bar.php:107
#: shortcodes/section-title/section-title.php:84
msgid "Title Color"
msgstr ""

#: post-types/clients/shortcodes/clients.php:147
#: shortcodes/counter/counter.php:126
#: shortcodes/image-with-text/image-with-text.php:102
#: shortcodes/process-2/process-2-item.php:90
msgid "Title Top Margin (px)"
msgstr ""

#: post-types/clients/shortcodes/clients.php:154
#: shortcodes/icon-progress-bar/icon-progress-bar.php:117
#: shortcodes/progress-bar/progress-bar.php:114
msgid "Title Bottom Margin (px)"
msgstr ""

#: post-types/clients/shortcodes/clients.php:164
#: post-types/clients/shortcodes/clients.php:172
msgid "Gallery Options"
msgstr ""

#: post-types/clients/shortcodes/clients.php:177
#: shortcodes/image-gallery/image-gallery.php:133
#: shortcodes/post-carousel/post-carousel.php:163
msgid "Number of Visible Items"
msgstr ""

#: post-types/clients/shortcodes/clients.php:187
#: post-types/clients/shortcodes/clients.php:195
#: post-types/clients/shortcodes/clients.php:203
#: post-types/clients/shortcodes/clients.php:211
#: post-types/clients/shortcodes/clients.php:219
#: post-types/clients/shortcodes/clients.php:227
#: post-types/clients/shortcodes/clients.php:235
#: post-types/clients/shortcodes/clients.php:243
#: post-types/clients/shortcodes/clients.php:251
#: post-types/clients/shortcodes/clients.php:262
#: post-types/testimonials/shortcodes/testimonials.php:109
#: post-types/testimonials/shortcodes/testimonials.php:116
#: post-types/testimonials/shortcodes/testimonials.php:123
#: post-types/testimonials/shortcodes/testimonials.php:130
#: post-types/testimonials/shortcodes/testimonials.php:137
#: post-types/testimonials/shortcodes/testimonials.php:144
#: post-types/testimonials/shortcodes/testimonials.php:151
#: post-types/testimonials/shortcodes/testimonials.php:158
#: post-types/testimonials/shortcodes/testimonials.php:168
#: shortcodes/image-gallery/image-gallery.php:143
#: shortcodes/image-gallery/image-gallery.php:151
#: shortcodes/image-gallery/image-gallery.php:159
#: shortcodes/image-gallery/image-gallery.php:167
#: shortcodes/image-gallery/image-gallery.php:175
#: shortcodes/image-gallery/image-gallery.php:183
#: shortcodes/image-gallery/image-gallery.php:191
#: shortcodes/image-gallery/image-gallery.php:199
#: shortcodes/image-gallery/image-gallery.php:207
#: shortcodes/image-gallery/image-gallery.php:218
#: shortcodes/post-carousel/post-carousel.php:172
#: shortcodes/post-carousel/post-carousel.php:179
#: shortcodes/post-carousel/post-carousel.php:186
#: shortcodes/post-carousel/post-carousel.php:193
#: shortcodes/post-carousel/post-carousel.php:200
#: shortcodes/post-carousel/post-carousel.php:207
#: shortcodes/post-carousel/post-carousel.php:214
#: shortcodes/post-carousel/post-carousel.php:221
#: shortcodes/post-carousel/post-carousel.php:228
#: shortcodes/post-carousel/post-carousel.php:238
msgid "Slider Options"
msgstr ""

#: post-types/clients/shortcodes/clients.php:192
#: post-types/testimonials/shortcodes/testimonials.php:107
#: shortcodes/image-gallery/image-gallery.php:148
#: shortcodes/post-carousel/post-carousel.php:177
msgid "Enable Slider Loop"
msgstr ""

#: post-types/clients/shortcodes/clients.php:200
#: post-types/testimonials/shortcodes/testimonials.php:114
#: shortcodes/image-gallery/image-gallery.php:156
#: shortcodes/post-carousel/post-carousel.php:184
msgid "Enable Slider Autoplay"
msgstr ""

#: post-types/clients/shortcodes/clients.php:208
#: post-types/testimonials/shortcodes/testimonials.php:121
#: shortcodes/image-gallery/image-gallery.php:164
#: shortcodes/post-carousel/post-carousel.php:191
msgid "Enable Slider Autoplay Hover Pause"
msgstr ""

#: post-types/clients/shortcodes/clients.php:216
#: post-types/testimonials/shortcodes/testimonials.php:128
#: shortcodes/image-gallery/image-gallery.php:172
#: shortcodes/post-carousel/post-carousel.php:198
msgid "Slide Duration (ms)"
msgstr ""

#: post-types/clients/shortcodes/clients.php:217
#: post-types/testimonials/shortcodes/testimonials.php:129
#: shortcodes/image-gallery/image-gallery.php:173
#: shortcodes/post-carousel/post-carousel.php:199
msgid "Speed of slide in milliseconds. Default value is 5000"
msgstr ""

#: post-types/clients/shortcodes/clients.php:224
#: post-types/testimonials/shortcodes/testimonials.php:135
#: shortcodes/image-gallery/image-gallery.php:180
#: shortcodes/post-carousel/post-carousel.php:205
msgid "Slide Animation Duration (ms)"
msgstr ""

#: post-types/clients/shortcodes/clients.php:225
#: post-types/testimonials/shortcodes/testimonials.php:136
#: shortcodes/image-gallery/image-gallery.php:181
#: shortcodes/post-carousel/post-carousel.php:206
msgid "Speed of slide animation in milliseconds. Default value is 600"
msgstr ""

#: post-types/clients/shortcodes/clients.php:232
#: post-types/testimonials/shortcodes/testimonials.php:142
#: shortcodes/image-gallery/image-gallery.php:188
#: shortcodes/post-carousel/post-carousel.php:212
msgid "Slide Margin (px)"
msgstr ""

#: post-types/clients/shortcodes/clients.php:233
#: post-types/testimonials/shortcodes/testimonials.php:143
#: shortcodes/image-gallery/image-gallery.php:189
#: shortcodes/post-carousel/post-carousel.php:213
msgid "Define right margin for slide items. Default value is 0"
msgstr ""

#: post-types/clients/shortcodes/clients.php:240
#: post-types/testimonials/shortcodes/testimonials.php:149
#: shortcodes/image-gallery/image-gallery.php:196
#: shortcodes/post-carousel/post-carousel.php:219
msgid "Enable Slider Navigation"
msgstr ""

#: post-types/clients/shortcodes/clients.php:248
#: post-types/testimonials/shortcodes/testimonials.php:156
#: shortcodes/image-gallery/image-gallery.php:204
#: shortcodes/post-carousel/post-carousel.php:226
msgid "Enable Slider Pagination"
msgstr ""

#: post-types/clients/shortcodes/clients.php:256
#: post-types/testimonials/shortcodes/testimonials.php:163
#: shortcodes/image-gallery/image-gallery.php:212
#: shortcodes/post-carousel/post-carousel.php:233
msgid "Slider Navigation Skin"
msgstr ""

#: post-types/clients/shortcodes/clients.php:259
#: post-types/testimonials/shortcodes/testimonials.php:166
#: shortcodes/blog-list/blog-list.php:140
#: shortcodes/blog-list/widgets/blog-list-widget.php:110
#: shortcodes/image-gallery/image-gallery.php:215
#: shortcodes/post-carousel/post-carousel.php:114
#: shortcodes/post-carousel/post-carousel.php:236
#: shortcodes/tabs/tabs.php:102
msgid "Light"
msgstr ""

#: post-types/clients/shortcodes/templates/parts/not-found.php:8
#: post-types/testimonials/shortcodes/templates/parts/not-found.php:8
#: shortcodes/blog-list/templates/parts/not-found.php:8
#: shortcodes/post-carousel/templates/parts/not-found.php:8
msgid "Sorry, no posts matched your criteria"
msgstr ""

#: post-types/testimonials/admin/meta-boxes/testimonials-meta-boxes.php:26
#: post-types/testimonials/shortcodes/testimonials.php:24
msgid "Testimonials"
msgstr ""

#: post-types/testimonials/admin/meta-boxes/testimonials-meta-boxes.php:80
#: shortcodes/blockquote/blockquote.php:69
#: shortcodes/button/button.php:92
#: shortcodes/button/widgets/button-widget.php:72
#: shortcodes/counter/counter.php:133
#: shortcodes/dropcaps/dropcaps.php:114
#: shortcodes/flip-image/flip-image.php:111
#: shortcodes/icon-list/icon-list-item.php:84
#: shortcodes/icon-with-text/icon-with-text.php:113
#: shortcodes/image-with-text/image-with-text.php:109
#: shortcodes/interactive-banner/interactive-banner.php:134
#: shortcodes/process-2/process-2-item.php:97
#: shortcodes/process/process-item.php:79
#: shortcodes/section-title/section-title.php:125
#: shortcodes/svg-text/svg-text.php:58
#: shortcodes/text-marquee/text-marquee.php:58
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:42
msgid "Text"
msgstr ""

#: post-types/testimonials/admin/meta-boxes/testimonials-meta-boxes.php:89
msgid "Author"
msgstr ""

#: post-types/testimonials/admin/meta-boxes/testimonials-meta-boxes.php:98
msgid "Author Job Position"
msgstr ""

#: post-types/testimonials/shortcodes/testimonials.php:77
msgid "Number of Testimonials"
msgstr ""

#: post-types/testimonials/shortcodes/testimonials.php:78
msgid "Enter number of testimonials or leave empty for showing all testimonials"
msgstr ""

#: post-types/testimonials/shortcodes/testimonials.php:101
#: shortcodes/flip-image/flip-image.php:74
#: shortcodes/gallery-block/gallery-block.php:86
#: shortcodes/image-gallery/image-gallery.php:92
#: shortcodes/image-with-text/image-with-text.php:64
#: shortcodes/interactive-banner/interactive-banner.php:78
#: shortcodes/single-image/single-image.php:64
msgid "Image Size"
msgstr ""

#: post-types/testimonials/shortcodes/testimonials.php:102
#: shortcodes/flip-image/flip-image.php:75
#: shortcodes/gallery-block/gallery-block.php:81
#: shortcodes/gallery-block/gallery-block.php:87
#: shortcodes/image-gallery/image-gallery.php:93
#: shortcodes/image-with-text/image-with-text.php:65
#: shortcodes/interactive-banner/interactive-banner.php:79
#: shortcodes/single-image/single-image.php:65
msgid "Fill your image size (thumbnail, medium, large or full) or enter image size in pixels: 200x100 (width x height). Leave empty to use original image size"
msgstr ""

#: post-types/testimonials/testimonials-class.php:61
#: post-types/testimonials/testimonials-class.php:62
msgid "EVC Testimonials"
msgstr ""

#: post-types/testimonials/testimonials-class.php:63
msgid "Testimonial"
msgstr ""

#: post-types/testimonials/testimonials-class.php:64
msgid "New Testimonial"
msgstr ""

#: post-types/testimonials/testimonials-class.php:65
msgid "Add New Testimonial"
msgstr ""

#: post-types/testimonials/testimonials-class.php:66
msgid "Edit Testimonial"
msgstr ""

#: post-types/testimonials/testimonials-class.php:86
#: post-types/testimonials/testimonials-class.php:96
msgid "Testimonials Categories"
msgstr ""

#: post-types/testimonials/testimonials-class.php:87
msgid "Testimonial Category"
msgstr ""

#: post-types/testimonials/testimonials-class.php:88
msgid "Search Testimonials Categories"
msgstr ""

#: post-types/testimonials/testimonials-class.php:89
msgid "All Testimonials Categories"
msgstr ""

#: post-types/testimonials/testimonials-class.php:90
msgid "Parent Testimonial Category"
msgstr ""

#: post-types/testimonials/testimonials-class.php:91
msgid "Parent Testimonial Category:"
msgstr ""

#: post-types/testimonials/testimonials-class.php:92
msgid "Edit Testimonials Category"
msgstr ""

#: post-types/testimonials/testimonials-class.php:93
msgid "Update Testimonials Category"
msgstr ""

#: post-types/testimonials/testimonials-class.php:94
msgid "Add New Testimonials Category"
msgstr ""

#: post-types/testimonials/testimonials-class.php:95
msgid "New Testimonials Category Name"
msgstr ""

#: shortcodes/blockquote/blockquote.php:24
msgid "Blockquote"
msgstr ""

#: shortcodes/blockquote/blockquote.php:61
msgid "Left Line"
msgstr ""

#: shortcodes/blockquote/blockquote.php:62
msgid "With Icon"
msgstr ""

#: shortcodes/blockquote/blockquote.php:74
#: shortcodes/counter/counter.php:138
#: shortcodes/dropcaps/dropcaps.php:119
#: shortcodes/flip-image/flip-image.php:116
#: shortcodes/icon-list/icon-list-item.php:90
#: shortcodes/icon-with-text/icon-with-text.php:118
#: shortcodes/image-with-text/image-with-text.php:114
#: shortcodes/interactive-banner/interactive-banner.php:139
#: shortcodes/process-2/process-2-item.php:102
#: shortcodes/process/process-item.php:84
#: shortcodes/section-title/section-title.php:138
msgid "Text Color"
msgstr ""

#: shortcodes/blockquote/blockquote.php:76
#: shortcodes/blockquote/blockquote.php:83
#: shortcodes/blockquote/blockquote.php:90
#: shortcodes/button/button.php:106
#: shortcodes/button/button.php:112
#: shortcodes/button/button.php:118
#: shortcodes/button/button.php:125
#: shortcodes/button/button.php:132
#: shortcodes/button/button.php:138
#: shortcodes/button/button.php:145
#: shortcodes/custom-font/custom-font.php:81
#: shortcodes/custom-font/custom-font.php:87
#: shortcodes/custom-font/custom-font.php:93
#: shortcodes/custom-font/custom-font.php:100
#: shortcodes/custom-font/custom-font.php:107
#: shortcodes/custom-font/custom-font.php:113
#: shortcodes/custom-font/custom-font.php:120
#: shortcodes/custom-font/custom-font.php:127
#: shortcodes/custom-font/custom-font.php:140
#: shortcodes/svg-text/svg-text.php:69
#: shortcodes/svg-text/svg-text.php:75
#: shortcodes/svg-text/svg-text.php:82
#: shortcodes/svg-text/svg-text.php:89
#: shortcodes/text-marquee/text-marquee.php:70
#: shortcodes/text-marquee/text-marquee.php:76
#: shortcodes/text-marquee/text-marquee.php:83
#: shortcodes/text-marquee/text-marquee.php:90
#: shortcodes/text-marquee/text-marquee.php:96
#: shortcodes/text-marquee/text-marquee.php:103
msgid "Typography Options"
msgstr ""

#: shortcodes/blockquote/blockquote.php:88
#: shortcodes/button/button.php:117
#: shortcodes/button/widgets/button-widget.php:98
#: shortcodes/custom-font/custom-font.php:92
#: shortcodes/custom-font/custom-font.php:152
#: shortcodes/custom-font/custom-font.php:166
#: shortcodes/custom-font/custom-font.php:180
#: shortcodes/custom-font/custom-font.php:194
#: shortcodes/custom-font/custom-font.php:208
#: shortcodes/custom-font/custom-font.php:222
#: shortcodes/custom-font/widgets/custom-font-widget.php:74
#: shortcodes/custom-font/widgets/custom-font-widget.php:127
#: shortcodes/custom-font/widgets/custom-font-widget.php:139
#: shortcodes/custom-font/widgets/custom-font-widget.php:151
#: shortcodes/custom-font/widgets/custom-font-widget.php:163
#: shortcodes/custom-font/widgets/custom-font-widget.php:175
#: shortcodes/custom-font/widgets/custom-font-widget.php:187
#: shortcodes/text-marquee/text-marquee.php:75
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:57
msgid "Line Height (px or em)"
msgstr ""

#: shortcodes/blog-list/blog-list.php:24
msgid "Blog List"
msgstr ""

#: shortcodes/blog-list/blog-list.php:74
#: shortcodes/blog-list/widgets/blog-list-widget.php:53
msgid "Minimal"
msgstr ""

#: shortcodes/blog-list/blog-list.php:84
#: shortcodes/blog-list/widgets/blog-list-widget.php:64
msgid "Date On Image"
msgstr ""

#: shortcodes/blog-list/blog-list.php:85
#: shortcodes/blog-list/widgets/blog-list-widget.php:65
msgid "Boxed Layout"
msgstr ""

#: shortcodes/blog-list/blog-list.php:93
#: shortcodes/blog-list/widgets/blog-list-widget.php:72
#: shortcodes/post-carousel/post-carousel.php:88
msgid "Number of Posts"
msgstr ""

#: shortcodes/blog-list/blog-list.php:128
msgid "Pagination Type"
msgstr ""

#: shortcodes/blog-list/blog-list.php:130
msgid "No Pagination"
msgstr ""

#: shortcodes/blog-list/blog-list.php:131
#: shortcodes/blog-list/blog-list.php:245
msgid "Load More"
msgstr ""

#: shortcodes/blog-list/blog-list.php:137
#: shortcodes/blog-list/widgets/blog-list-widget.php:107
#: shortcodes/post-carousel/post-carousel.php:111
#: shortcodes/tabs/tabs.php:99
msgid "Skin"
msgstr ""

#: shortcodes/blog-list/blog-list.php:152
#: shortcodes/blog-list/widgets/blog-list-widget.php:123
#: shortcodes/post-carousel/post-carousel.php:126
msgid "Thumbnail"
msgstr ""

#: shortcodes/blog-list/blog-list.php:167
#: shortcodes/blog-list/widgets/blog-list-widget.php:136
#: shortcodes/post-carousel/post-carousel.php:140
msgid "Enable Excerpt"
msgstr ""

#: shortcodes/blog-list/blog-list.php:175
#: shortcodes/blog-list/widgets/blog-list-widget.php:143
#: shortcodes/post-carousel/post-carousel.php:147
msgid "Excerpt Length"
msgstr ""

#: shortcodes/blog-list/blog-list.php:176
#: shortcodes/blog-list/widgets/blog-list-widget.php:144
#: shortcodes/post-carousel/post-carousel.php:148
msgid "Set number of characters"
msgstr ""

#: shortcodes/blog-list/blog-list.php:191
#: shortcodes/blog-list/widgets/blog-list-widget.php:156
msgid "Enable Author"
msgstr ""

#: shortcodes/blog-list/blog-list.php:199
#: shortcodes/blog-list/widgets/blog-list-widget.php:163
#: shortcodes/post-carousel/post-carousel.php:155
msgid "Enable Date"
msgstr ""

#: shortcodes/blog-list/blog-list.php:206
#: shortcodes/pricing-table/pricing-table-item.php:153
#: shortcodes/section-title/section-title.php:152
msgid "Button Text"
msgstr ""

#: shortcodes/blog-list/templates/parts/author.php:10
msgid "by"
msgstr ""

#: shortcodes/blog-list/widgets/blog-list-widget.php:16
msgid "EVC Blog List"
msgstr ""

#: shortcodes/blog-list/widgets/blog-list-widget.php:17
msgid "Add blog list element to widget areas"
msgstr ""

#: shortcodes/blog-list/widgets/blog-list-widget.php:61
#: shortcodes/blog-list/widgets/blog-list-widget.php:150
#: shortcodes/blog-list/widgets/blog-list-widget.php:157
msgid "Only for standard layout"
msgstr ""

#: shortcodes/blog-list/widgets/blog-list-widget.php:118
#: shortcodes/blog-list/widgets/blog-list-widget.php:137
msgid "Only for standard and gallery layout"
msgstr ""

#: shortcodes/button/button.php:24
msgid "Button"
msgstr ""

#: shortcodes/button/button.php:67
#: shortcodes/button/widgets/button-widget.php:50
msgid "Simple Strike Line On Hover 2"
msgstr ""

#: shortcodes/button/button.php:83
msgid "Full Width"
msgstr ""

#: shortcodes/button/button.php:97
#: shortcodes/button/widgets/button-widget.php:77
#: shortcodes/flip-image/flip-image.php:80
#: shortcodes/full-screen-sections/full-screen-sections-item.php:109
#: shortcodes/icon-list/icon-list-item.php:97
#: shortcodes/icon-with-text/icon-with-text.php:132
#: shortcodes/image-with-text/image-with-text.php:76
#: shortcodes/interactive-banner/interactive-banner.php:153
#: shortcodes/process-2/process-2-item.php:65
#: shortcodes/single-image/single-image.php:76
#: shortcodes/svg-text/svg-text.php:63
msgid "Custom Link"
msgstr ""

#: shortcodes/button/button.php:105
#: shortcodes/button/widgets/button-widget.php:88
#: shortcodes/custom-font/custom-font.php:80
#: shortcodes/custom-font/widgets/custom-font-widget.php:64
msgid "Font Family"
msgstr ""

#: shortcodes/button/button.php:123
#: shortcodes/button/widgets/button-widget.php:103
#: shortcodes/custom-font/custom-font.php:98
#: shortcodes/custom-font/widgets/custom-font-widget.php:79
#: shortcodes/svg-text/svg-text.php:80
#: shortcodes/text-marquee/text-marquee.php:81
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:62
msgid "Font Weight"
msgstr ""

#: shortcodes/button/button.php:130
#: shortcodes/button/widgets/button-widget.php:109
#: shortcodes/custom-font/custom-font.php:105
#: shortcodes/custom-font/widgets/custom-font-widget.php:85
#: shortcodes/text-marquee/text-marquee.php:88
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:68
msgid "Font Style"
msgstr ""

#: shortcodes/button/button.php:137
#: shortcodes/button/widgets/button-widget.php:115
#: shortcodes/custom-font/custom-font.php:112
#: shortcodes/custom-font/widgets/custom-font-widget.php:91
#: shortcodes/text-marquee/text-marquee.php:95
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:74
msgid "Letter Spacing (px or em)"
msgstr ""

#: shortcodes/button/button.php:143
#: shortcodes/button/widgets/button-widget.php:120
#: shortcodes/custom-font/custom-font.php:118
#: shortcodes/custom-font/widgets/custom-font-widget.php:96
#: shortcodes/svg-text/svg-text.php:87
#: shortcodes/text-marquee/text-marquee.php:101
#: shortcodes/text-marquee/widgets/text-marquee-widget.php:79
msgid "Text Transform"
msgstr ""

#: shortcodes/button/button.php:218
#: shortcodes/button/widgets/button-widget.php:184
msgid "Button Alignment"
msgstr ""

#: shortcodes/button/button.php:221
#: shortcodes/button/widgets/button-widget.php:187
#: shortcodes/custom-font/custom-font.php:135
#: shortcodes/custom-font/widgets/custom-font-widget.php:111
#: shortcodes/doughnut-chart/doughnut-chart.php:125
#: shortcodes/full-screen-sections/full-screen-sections-item.php:101
#: shortcodes/pie-chart/pie-chart.php:125
#: shortcodes/section-title/section-title.php:62
#: shortcodes/separator/separator.php:61
#: shortcodes/separator/widgets/separator-widget.php:40
msgid "Left"
msgstr ""

#: shortcodes/button/button.php:222
#: shortcodes/button/widgets/button-widget.php:188
#: shortcodes/custom-font/custom-font.php:137
#: shortcodes/custom-font/widgets/custom-font-widget.php:113
#: shortcodes/doughnut-chart/doughnut-chart.php:123
#: shortcodes/full-screen-sections/full-screen-sections-item.php:103
#: shortcodes/pie-chart/pie-chart.php:123
#: shortcodes/section-title/section-title.php:64
#: shortcodes/separator/separator.php:62
#: shortcodes/separator/widgets/separator-widget.php:41
msgid "Right"
msgstr ""

#: shortcodes/button/button.php:223
#: shortcodes/button/widgets/button-widget.php:189
#: shortcodes/custom-font/custom-font.php:136
#: shortcodes/custom-font/widgets/custom-font-widget.php:112
#: shortcodes/full-screen-sections/full-screen-sections-item.php:102
#: shortcodes/section-title/section-title.php:63
#: shortcodes/separator/separator.php:60
#: shortcodes/separator/widgets/separator-widget.php:39
msgid "Center"
msgstr ""

#: shortcodes/button/widgets/button-widget.php:16
msgid "EVC Button"
msgstr ""

#: shortcodes/button/widgets/button-widget.php:17
msgid "Add button element to widget areas"
msgstr ""

#: shortcodes/button/widgets/button-widget.php:59
#: shortcodes/button/widgets/button-widget.php:137
#: shortcodes/button/widgets/button-widget.php:143
#: shortcodes/button/widgets/button-widget.php:149
#: shortcodes/button/widgets/button-widget.php:155
#: shortcodes/button/widgets/button-widget.php:161
msgid "Only for solid and outline button types"
msgstr ""

#: shortcodes/button/widgets/button-widget.php:167
msgid "Only for fill line, strike line and switch line button types"
msgstr ""

#: shortcodes/button/widgets/button-widget.php:173
msgid "Only for switch line button type"
msgstr ""

#: shortcodes/counter/counter.php:24
msgid "Counter"
msgstr ""

#: shortcodes/counter/counter.php:70
msgid "Digit"
msgstr ""

#: shortcodes/counter/counter.php:76
msgid "Digit Color"
msgstr ""

#: shortcodes/counter/counter.php:78
#: shortcodes/counter/counter.php:85
#: shortcodes/counter/counter.php:92
#: shortcodes/counter/counter.php:100
msgid "Digit Options"
msgstr ""

#: shortcodes/counter/counter.php:83
msgid "Digit Font Size (px or em)"
msgstr ""

#: shortcodes/counter/counter.php:90
msgid "Digit Line Height (px or em)"
msgstr ""

#: shortcodes/counter/counter.php:97
msgid "Digit Font Weight"
msgstr ""

#: shortcodes/counter/counter.php:140
#: shortcodes/counter/counter.php:147
#: shortcodes/dropcaps/dropcaps.php:120
#: shortcodes/icon-list/icon-list-item.php:92
#: shortcodes/icon-with-text/icon-with-text.php:120
#: shortcodes/icon-with-text/icon-with-text.php:127
#: shortcodes/interactive-banner/interactive-banner.php:141
#: shortcodes/interactive-banner/interactive-banner.php:148
#: shortcodes/section-title/section-title.php:133
#: shortcodes/section-title/section-title.php:140
#: shortcodes/section-title/section-title.php:147
msgid "Text Options"
msgstr ""

#: shortcodes/counter/counter.php:145
#: shortcodes/flip-image/flip-image.php:123
#: shortcodes/icon-with-text/icon-with-text.php:125
#: shortcodes/image-with-text/image-with-text.php:121
#: shortcodes/interactive-banner/interactive-banner.php:146
#: shortcodes/process-2/process-2-item.php:109
#: shortcodes/process/process-item.php:91
#: shortcodes/section-title/section-title.php:145
msgid "Text Top Margin (px)"
msgstr ""

#: shortcodes/custom-font/custom-font.php:24
msgid "Custom Font"
msgstr ""

#: shortcodes/custom-font/custom-font.php:74
#: shortcodes/custom-font/widgets/custom-font-widget.php:58
msgid "Margin (px or %)"
msgstr ""

#: shortcodes/custom-font/custom-font.php:125
#: shortcodes/custom-font/widgets/custom-font-widget.php:102
msgid "Text Decoration"
msgstr ""

#: shortcodes/custom-font/custom-font.php:132
#: shortcodes/custom-font/widgets/custom-font-widget.php:108
msgid "Text Align"
msgstr ""

#: shortcodes/custom-font/custom-font.php:138
#: shortcodes/custom-font/widgets/custom-font-widget.php:114
msgid "Justify"
msgstr ""

#: shortcodes/custom-font/custom-font.php:146
#: shortcodes/custom-font/widgets/custom-font-widget.php:122
#: shortcodes/svg-text/svg-text.php:95
msgid "Set font size for 1440px screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:147
#: shortcodes/custom-font/custom-font.php:154
#: shortcodes/custom-font/custom-font.php:161
#: shortcodes/custom-font/custom-font.php:168
#: shortcodes/custom-font/custom-font.php:175
#: shortcodes/custom-font/custom-font.php:182
#: shortcodes/custom-font/custom-font.php:189
#: shortcodes/custom-font/custom-font.php:196
#: shortcodes/custom-font/custom-font.php:203
#: shortcodes/custom-font/custom-font.php:210
#: shortcodes/custom-font/custom-font.php:217
#: shortcodes/custom-font/custom-font.php:224
#: shortcodes/svg-text/svg-text.php:96
#: shortcodes/svg-text/svg-text.php:103
#: shortcodes/svg-text/svg-text.php:110
#: shortcodes/svg-text/svg-text.php:117
#: shortcodes/svg-text/svg-text.php:124
#: shortcodes/svg-text/svg-text.php:131
msgid "Responsive Options"
msgstr ""

#: shortcodes/custom-font/custom-font.php:153
#: shortcodes/custom-font/widgets/custom-font-widget.php:128
msgid "Set line height for 1440px screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:160
#: shortcodes/custom-font/widgets/custom-font-widget.php:134
#: shortcodes/svg-text/svg-text.php:102
msgid "Set font size for 1366px screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:167
#: shortcodes/custom-font/widgets/custom-font-widget.php:140
msgid "Set line height for 1366px screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:174
#: shortcodes/custom-font/widgets/custom-font-widget.php:146
#: shortcodes/svg-text/svg-text.php:109
msgid "Set font size for 1280px screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:181
#: shortcodes/custom-font/widgets/custom-font-widget.php:152
msgid "Set line height for 1280px screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:188
#: shortcodes/custom-font/widgets/custom-font-widget.php:158
#: shortcodes/svg-text/svg-text.php:116
msgid "Set font size for tablet landscape screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:195
#: shortcodes/custom-font/widgets/custom-font-widget.php:164
msgid "Set line height for tablet landscape screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:202
#: shortcodes/custom-font/widgets/custom-font-widget.php:170
#: shortcodes/svg-text/svg-text.php:123
msgid "Set font size for tablet portrait screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:209
#: shortcodes/custom-font/widgets/custom-font-widget.php:176
msgid "Set line height for tablet portrait screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:216
#: shortcodes/custom-font/widgets/custom-font-widget.php:182
#: shortcodes/svg-text/svg-text.php:130
msgid "Set font size for mobiles screen size"
msgstr ""

#: shortcodes/custom-font/custom-font.php:223
#: shortcodes/custom-font/widgets/custom-font-widget.php:188
msgid "Set line height for mobiles screen size"
msgstr ""

#: shortcodes/custom-font/widgets/custom-font-widget.php:16
msgid "EVC Custom Font"
msgstr ""

#: shortcodes/custom-font/widgets/custom-font-widget.php:17
msgid "Add custom font element to widget areas"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart-item.php:25
msgid "Doughnut Chart Item"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart-item.php:53
#: shortcodes/line-graph/line-graph-item.php:53
#: shortcodes/pie-chart/pie-chart-item.php:53
msgid "Label"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart-item.php:58
#: shortcodes/line-graph/line-graph-item.php:58
#: shortcodes/pie-chart/pie-chart-item.php:58
msgid "Value"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:25
msgid "Doughnut Chart"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:83
#: shortcodes/pie-chart/pie-chart.php:83
msgid "Canvas Width (px)"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:84
#: shortcodes/pie-chart/pie-chart.php:84
msgid "Fill canvas width size, default value is 260"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:89
#: shortcodes/pie-chart/pie-chart.php:89
msgid "Canvas Height (px)"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:90
#: shortcodes/pie-chart/pie-chart.php:90
msgid "Fill canvas height size, default value is 260"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:95
#: shortcodes/pie-chart/pie-chart.php:95
msgid "Canvas Space"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:96
#: shortcodes/pie-chart/pie-chart.php:96
msgid "Fill space between items, default value is 2"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:101
#: shortcodes/pie-chart/pie-chart.php:101
msgid "Canvas Space Color"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:107
#: shortcodes/pie-chart/pie-chart.php:107
msgid "Canvas Space Hover Color"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:113
#: shortcodes/pie-chart/pie-chart.php:113
msgid "Enable Legend"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:120
#: shortcodes/pie-chart/pie-chart.php:120
msgid "Legend Position"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:122
#: shortcodes/full-screen-sections/full-screen-sections-item.php:90
#: shortcodes/pie-chart/pie-chart.php:122
msgid "Top"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:124
#: shortcodes/full-screen-sections/full-screen-sections-item.php:92
#: shortcodes/pie-chart/pie-chart.php:124
msgid "Bottom"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:133
#: shortcodes/pie-chart/pie-chart.php:133
msgid "Legend Text Size (px)"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:134
#: shortcodes/pie-chart/pie-chart.php:134
msgid "Fill legend text font size, default value is 12"
msgstr ""

#: shortcodes/doughnut-chart/doughnut-chart.php:141
#: shortcodes/pie-chart/pie-chart.php:141
msgid "Legend Color"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:24
msgid "Dropcaps"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:61
msgid "Circle"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:62
msgid "Square"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:69
msgid "Letter"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:75
msgid "Letter Color"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:76
#: shortcodes/dropcaps/dropcaps.php:83
#: shortcodes/dropcaps/dropcaps.php:89
#: shortcodes/dropcaps/dropcaps.php:95
#: shortcodes/dropcaps/dropcaps.php:102
#: shortcodes/dropcaps/dropcaps.php:109
msgid "Letter Options"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:81
msgid "Letter Background Color"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:88
msgid "Letter Font Size (px or em)"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:94
msgid "Letter Line Height (px or em)"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:100
msgid "Letter Font Weight"
msgstr ""

#: shortcodes/dropcaps/dropcaps.php:107
msgid "Letter Margin"
msgstr ""

#: shortcodes/flip-image/flip-image.php:24
msgid "Flip Image"
msgstr ""

#: shortcodes/flip-image/flip-image.php:60
#: shortcodes/progress-bar/progress-bar.php:72
msgid "Horizontal"
msgstr ""

#: shortcodes/flip-image/flip-image.php:61
#: shortcodes/progress-bar/progress-bar.php:73
#: shortcodes/tabs/tabs.php:79
msgid "Vertical"
msgstr ""

#: shortcodes/flip-image/flip-image.php:69
#: shortcodes/image-with-text/image-with-text.php:59
#: shortcodes/interactive-banner/interactive-banner.php:73
#: shortcodes/process-2/process-2-item.php:60
#: shortcodes/process-2/process-2.php:97
#: shortcodes/single-image/single-image.php:59
msgid "Select image from media library"
msgstr ""

#: shortcodes/flip-image/flip-image.php:85
msgid "Content Background Color"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:25
msgid "Full Screen Sections Item"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:64
msgid "Background Image"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:69
msgid "Background Image Position"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:70
msgid "Please insert position in format horizontal vertical position, example - center center"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:76
msgid "Background Image Size"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:78
msgid "Cover"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:79
msgid "Contain"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:87
msgid "Content Vertical Alignment"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:91
msgid "Middle"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections-item.php:98
msgid "Content Horizontal Alignment"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections.php:25
msgid "Full Screen Sections"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections.php:83
msgid "Enable Navigation Arrows"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections.php:89
msgid "Top Margin Offset (px)"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections.php:90
msgid "Defines top margin offset to put shortcode behind header if header element is not transparent"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections.php:95
msgid "Choose Slide Animation"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections.php:97
msgid "Predefined"
msgstr ""

#: shortcodes/full-screen-sections/full-screen-sections.php:98
msgid "Slide"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:24
msgid "Gallery Block"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:60
msgid "Featured Image Top"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:61
msgid "Featured Image On Left Side"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:74
#: shortcodes/image-gallery/image-gallery.php:86
msgid "Images"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:75
msgid "Select images from media library. The first image you upload will be set as the featured image if you set Featured Image Size"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:80
msgid "Featured Image Size"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:92
#: shortcodes/image-gallery/image-gallery.php:98
#: shortcodes/image-with-text/image-with-text.php:70
#: shortcodes/single-image/single-image.php:70
msgid "Image Behavior"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:98
#: shortcodes/image-gallery/image-gallery.php:104
msgid "Custom Links"
msgstr ""

#: shortcodes/gallery-block/gallery-block.php:99
#: shortcodes/image-gallery/image-gallery.php:105
msgid "Delimit links by comma"
msgstr ""

#: shortcodes/icon-list/icon-list-item.php:25
msgid "Icon List Item"
msgstr ""

#: shortcodes/icon-list/icon-list-item.php:63
#: shortcodes/icon-progress-bar/icon-progress-bar.php:72
#: shortcodes/icon-with-text/icon-with-text.php:77
#: shortcodes/interactive-banner/interactive-banner.php:99
msgid "Icon Size (px)"
msgstr ""

#: shortcodes/icon-list/icon-list-item.php:65
#: shortcodes/icon-list/icon-list-item.php:72
#: shortcodes/icon-list/icon-list-item.php:79
#: shortcodes/icon-progress-bar/icon-progress-bar.php:73
#: shortcodes/icon-progress-bar/icon-progress-bar.php:79
#: shortcodes/icon-progress-bar/icon-progress-bar.php:85
#: shortcodes/icon-progress-bar/icon-progress-bar.php:91
#: shortcodes/icon-with-text/icon-with-text.php:79
#: shortcodes/icon-with-text/icon-with-text.php:86
#: shortcodes/interactive-banner/interactive-banner.php:101
#: shortcodes/interactive-banner/interactive-banner.php:108
msgid "Icon Options"
msgstr ""

#: shortcodes/icon-list/icon-list-item.php:70
#: shortcodes/icon-progress-bar/icon-progress-bar.php:78
#: shortcodes/icon-with-text/icon-with-text.php:84
#: shortcodes/interactive-banner/interactive-banner.php:106
msgid "Icon Color"
msgstr ""

#: shortcodes/icon-list/icon-list-item.php:77
msgid "Icon Right Padding (px)"
msgstr ""

#: shortcodes/icon-list/icon-list-item.php:102
msgid "Space Between Items (px)"
msgstr ""

#: shortcodes/icon-list/icon-list-item.php:103
msgid "Fill space between items in your list. Default value is 8"
msgstr ""

#: shortcodes/icon-list/icon-list.php:25
msgid "Icon List"
msgstr ""

#: shortcodes/icon-progress-bar/icon-progress-bar.php:24
msgid "Icon Progress Bar"
msgstr ""

#: shortcodes/icon-progress-bar/icon-progress-bar.php:59
msgid "Number Of Icons"
msgstr ""

#: shortcodes/icon-progress-bar/icon-progress-bar.php:64
msgid "Number Of Active Icons"
msgstr ""

#: shortcodes/icon-progress-bar/icon-progress-bar.php:84
msgid "Icon Active Color"
msgstr ""

#: shortcodes/icon-progress-bar/icon-progress-bar.php:90
msgid "Space Between Icons (px)"
msgstr ""

#: shortcodes/icon-with-text/icon-with-text.php:24
msgid "Icon With Text"
msgstr ""

#: shortcodes/icon-with-text/icon-with-text.php:61
msgid "Icon Top"
msgstr ""

#: shortcodes/icon-with-text/icon-with-text.php:62
msgid "Icon Left"
msgstr ""

#: shortcodes/icon-with-text/icon-with-text.php:72
#: shortcodes/interactive-banner/interactive-banner.php:94
msgid "Custom Icon"
msgstr ""

#: shortcodes/image-gallery/image-gallery.php:24
msgid "Image Gallery"
msgstr ""

#: shortcodes/image-gallery/image-gallery.php:76
msgid "Image Grid"
msgstr ""

#: shortcodes/image-gallery/image-gallery.php:78
msgid "Carousel"
msgstr ""

#: shortcodes/image-gallery/image-gallery.php:87
msgid "Select images from media library"
msgstr ""

#: shortcodes/image-gallery/image-gallery.php:120
#: shortcodes/image-gallery/image-gallery.php:128
msgid "Grid Options"
msgstr ""

#: shortcodes/image-with-text/image-with-text.php:24
msgid "Image With Text"
msgstr ""

#: shortcodes/interactive-banner/interactive-banner.php:24
msgid "Interactive Banner"
msgstr ""

#: shortcodes/interactive-banner/interactive-banner.php:61
msgid "Classic"
msgstr ""

#: shortcodes/interactive-banner/interactive-banner.php:62
msgid "Bottom Animation"
msgstr ""

#: shortcodes/interactive-banner/interactive-banner.php:64
#: shortcodes/tabs/tabs.php:92
msgid "Slide From Bottom"
msgstr ""

#: shortcodes/interactive-banner/interactive-banner.php:65
msgid "Shutter In Vertical"
msgstr ""

#: shortcodes/interactive-banner/interactive-banner.php:85
msgid "Overlay Color"
msgstr ""

#: shortcodes/line-graph/line-graph-item.php:25
msgid "Line Graph Item"
msgstr ""

#: shortcodes/line-graph/line-graph.php:25
msgid "Line Graph"
msgstr ""

#: shortcodes/line-graph/line-graph.php:83
msgid "Legend Text"
msgstr ""

#: shortcodes/line-graph/line-graph.php:94
msgid "Line Thickness (px)"
msgstr ""

#: shortcodes/line-graph/line-graph.php:100
msgid "Disable Line"
msgstr ""

#: shortcodes/line-graph/line-graph.php:101
msgid "Enabling this option will hide line on graph"
msgstr ""

#: shortcodes/line-graph/line-graph.php:108
msgid "Fill Background Color"
msgstr ""

#: shortcodes/line-graph/line-graph.php:109
msgid "Set background color under the line"
msgstr ""

#: shortcodes/line-graph/line-graph.php:129
msgid "Legend"
msgstr ""

#: shortcodes/pie-chart/pie-chart-item.php:25
msgid "Pie Chart Item"
msgstr ""

#: shortcodes/pie-chart/pie-chart.php:25
msgid "Pie Chart"
msgstr ""

#: shortcodes/post-carousel/post-carousel.php:24
msgid "Post Carousel"
msgstr ""

#: shortcodes/post-carousel/post-carousel.php:80
#: shortcodes/tabs/tabs.php:80
msgid "Centered"
msgstr ""

#: shortcodes/post-carousel/post-carousel.php:81
msgid "Sliding Excerpt"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:25
msgid "Pricing Table Item"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:80
msgid "Price"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:85
msgid "Price Background Color"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:92
msgid "Price Background Image"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:99
msgid "Price Color"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:106
msgid "Price Size (px)"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:113
msgid "Currency"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:114
msgid "Default mark is $"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:119
msgid "Currency Color"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:126
msgid "Currency Size (px)"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:133
msgid "Price Period"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:134
msgid "Default label is monthly"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:139
msgid "Price Period Color"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:146
msgid "Price Period Size (px)"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:161
msgid "Content"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:162
msgid "This is pricing table item content"
msgstr ""

#: shortcodes/pricing-table/pricing-table-item.php:168
#: shortcodes/pricing-table/pricing-table-item.php:174
msgid "Content Options"
msgstr ""

#: shortcodes/pricing-table/pricing-table.php:25
msgid "Pricing Table"
msgstr ""

#: shortcodes/process-2/process-2-item.php:25
msgid "Process 2 Item"
msgstr ""

#: shortcodes/process-2/process-2.php:25
msgid "Process 2"
msgstr ""

#: shortcodes/process-2/process-2.php:81
#: shortcodes/process/process.php:81
msgid "Switch to One Column"
msgstr ""

#: shortcodes/process-2/process-2.php:83
#: shortcodes/process/process.php:83
msgid "Default None"
msgstr ""

#: shortcodes/process-2/process-2.php:84
#: shortcodes/process/process.php:84
msgid "Below 1366px"
msgstr ""

#: shortcodes/process-2/process-2.php:85
#: shortcodes/process/process.php:85
msgid "Below 1280px"
msgstr ""

#: shortcodes/process-2/process-2.php:86
#: shortcodes/process/process.php:86
msgid "Below 1024px"
msgstr ""

#: shortcodes/process-2/process-2.php:87
#: shortcodes/process/process.php:87
msgid "Below 768px"
msgstr ""

#: shortcodes/process-2/process-2.php:88
#: shortcodes/process/process.php:88
msgid "Below 680px"
msgstr ""

#: shortcodes/process-2/process-2.php:89
#: shortcodes/process/process.php:89
msgid "Below 480px"
msgstr ""

#: shortcodes/process-2/process-2.php:91
#: shortcodes/process/process.php:91
msgid "Choose on which stage item will be full width"
msgstr ""

#: shortcodes/process-2/process-2.php:96
msgid "Background Cover Image"
msgstr ""

#: shortcodes/process/process-item.php:25
msgid "Process Item"
msgstr ""

#: shortcodes/process/process.php:25
msgid "Process"
msgstr ""

#: shortcodes/process/process.php:96
msgid "Circle Number Color"
msgstr ""

#: shortcodes/process/process.php:102
msgid "Circle Background Color"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:24
msgid "Progress Bar"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:81
msgid "Percentage"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:86
msgid "Percentage Color"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:88
msgid "Percentage Options"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:121
msgid "Active Bar Color"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:122
#: shortcodes/progress-bar/progress-bar.php:128
msgid "Bar Options"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:127
msgid "Inactive Bar Color"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:133
msgid "Bar Height (px)"
msgstr ""

#: shortcodes/progress-bar/progress-bar.php:134
msgid "Bar Settings"
msgstr ""

#: shortcodes/section-title/section-title.php:24
msgid "Section Title"
msgstr ""

#: shortcodes/section-title/section-title.php:59
msgid "Text Alignment"
msgstr ""

#: shortcodes/section-title/section-title.php:91
msgid "Enable Separator"
msgstr ""

#: shortcodes/section-title/section-title.php:97
msgid "Separator Color"
msgstr ""

#: shortcodes/section-title/section-title.php:99
#: shortcodes/section-title/section-title.php:106
#: shortcodes/section-title/section-title.php:113
#: shortcodes/section-title/section-title.php:120
msgid "Separator Options"
msgstr ""

#: shortcodes/section-title/section-title.php:104
msgid "Separator Width (px or %)"
msgstr ""

#: shortcodes/section-title/section-title.php:111
msgid "Separator Thickness (px)"
msgstr ""

#: shortcodes/section-title/section-title.php:118
msgid "Separator Top Margin (px)"
msgstr ""

#: shortcodes/section-title/section-title.php:130
msgid "Text Tag"
msgstr ""

#: shortcodes/separator/separator.php:24
msgid "Separator"
msgstr ""

#: shortcodes/separator/separator.php:58
#: shortcodes/separator/widgets/separator-widget.php:37
msgid "Position"
msgstr ""

#: shortcodes/separator/separator.php:68
#: shortcodes/separator/widgets/separator-widget.php:48
msgid "Width (px or %)"
msgstr ""

#: shortcodes/separator/separator.php:73
#: shortcodes/separator/widgets/separator-widget.php:53
msgid "Thickness (px)"
msgstr ""

#: shortcodes/separator/separator.php:78
#: shortcodes/separator/widgets/separator-widget.php:58
msgid "Style"
msgstr ""

#: shortcodes/separator/separator.php:81
#: shortcodes/separator/widgets/separator-widget.php:61
msgid "Dashed"
msgstr ""

#: shortcodes/separator/separator.php:83
#: shortcodes/separator/widgets/separator-widget.php:63
msgid "Dotted"
msgstr ""

#: shortcodes/separator/separator.php:89
#: shortcodes/separator/widgets/separator-widget.php:70
msgid "Top Margin (px or %)"
msgstr ""

#: shortcodes/separator/separator.php:95
#: shortcodes/separator/widgets/separator-widget.php:75
msgid "Bottom Margin (px or %)"
msgstr ""

#: shortcodes/separator/widgets/separator-widget.php:16
msgid "EVC Separator"
msgstr ""

#: shortcodes/separator/widgets/separator-widget.php:17
msgid "Add separator element to widget areas"
msgstr ""

#: shortcodes/shortcodes-extends-class.php:315
msgid "EVC "
msgstr ""

#: shortcodes/shortcodes-functions.php:116
msgid "Post is invalid"
msgstr ""

#: shortcodes/shortcodes-functions.php:135
msgid "Items are loaded"
msgstr ""

#: shortcodes/shortcodes-functions.php:137
msgid "Options are invalid"
msgstr ""

#: shortcodes/single-image/single-image.php:24
msgid "Single Image"
msgstr ""

#: shortcodes/svg-text/svg-text.php:24
msgid "SVG Text"
msgstr ""

#: shortcodes/svg-text/svg-text.php:74
#: shortcodes/svg-text/svg-text.php:94
#: shortcodes/svg-text/svg-text.php:101
#: shortcodes/svg-text/svg-text.php:108
#: shortcodes/svg-text/svg-text.php:115
#: shortcodes/svg-text/svg-text.php:122
#: shortcodes/svg-text/svg-text.php:129
msgid "Font Size (px)"
msgstr ""

#: shortcodes/tabs/tabs-item.php:25
msgid "Tabs Item"
msgstr ""

#: shortcodes/tabs/tabs-item.php:77
msgid "Tab Title"
msgstr ""

#: shortcodes/tabs/tabs.php:25
msgid "Tabs"
msgstr ""

#: shortcodes/tabs/tabs.php:87
msgid "Animation Type"
msgstr ""

#: shortcodes/tabs/tabs.php:88
msgid "Choose tab content animation on item click"
msgstr ""

#: shortcodes/tabs/tabs.php:91
msgid "Fade"
msgstr ""

#: shortcodes/tabs/tabs.php:93
msgid "Slide From Right"
msgstr ""

#: shortcodes/text-marquee/text-marquee.php:24
msgid "Text Marquee"
msgstr ""

#: shortcodes/text-marquee/widgets/text-marquee-widget.php:16
msgid "EVC Text Marquee"
msgstr ""

#: shortcodes/text-marquee/widgets/text-marquee-widget.php:17
msgid "Add text marquee element to widget areas"
msgstr ""

#: widgets/widgets-class.php:105
msgid "Please add some options for this widget"
msgstr ""
